-- 修复积分系统视图安全问题
-- 删除有安全风险的视图并重新创建安全版本

-- ============================================================================
-- 1. 删除有安全风险的视图
-- ============================================================================

-- 删除 credits_index_usage 视图（存在 SECURITY DEFINER 风险）
DROP VIEW IF EXISTS credits_index_usage;

-- 删除测试性能函数（如果存在）
DROP FUNCTION IF EXISTS test_credits_query_performance();

-- ============================================================================
-- 2. 创建安全的替代查询（不使用视图）
-- ============================================================================

-- 说明：我们不再创建视图，而是提供直接的查询语句
-- 这样避免了 SECURITY DEFINER 的安全风险

-- 查询索引使用情况的安全SQL（管理员可以直接运行）
/*
-- 安全的索引监控查询（直接运行，不创建视图）
SELECT 
    schemaname,
    relname as tablename,
    indexrelname as indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE relname = 'credits'
ORDER BY idx_scan DESC;
*/

-- ============================================================================
-- 3. 验证索引状态（安全查询）
-- ============================================================================

-- 验证我们创建的索引是否存在且正常工作
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'credits' 
AND indexname LIKE 'idx_credits_%'
ORDER BY indexname;

-- ============================================================================
-- 4. 检查索引大小和使用情况
-- ============================================================================

-- 查看索引大小（帮助评估索引效果）
SELECT 
    indexrelname as index_name,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
    idx_scan as times_used,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE relname = 'credits'
ORDER BY idx_scan DESC;

-- ============================================================================
-- 5. 安全的性能测试查询
-- ============================================================================

-- 测试用户积分查询性能（替换 your-test-uuid）
-- EXPLAIN ANALYZE 
-- SELECT * FROM credits 
-- WHERE user_uuid = 'your-test-uuid' 
-- AND expired_at >= NOW() 
-- ORDER BY expired_at ASC;

-- 测试用户交易记录查询性能
-- EXPLAIN ANALYZE 
-- SELECT * FROM credits 
-- WHERE user_uuid = 'your-test-uuid' 
-- ORDER BY created_at DESC 
-- LIMIT 50;

-- ============================================================================
-- 6. 清理和安全确认
-- ============================================================================

-- 确认没有其他有安全风险的对象
SELECT 
    'VIEW' as object_type,
    viewname as object_name,
    'Potential security risk if using SECURITY DEFINER' as note
FROM pg_views 
WHERE schemaname = 'public' 
AND viewname LIKE '%credits%'

UNION ALL

SELECT 
    'FUNCTION' as object_type,
    proname as object_name,
    CASE 
        WHEN prosecdef THEN 'SECURITY DEFINER - Review required'
        ELSE 'SECURITY INVOKER - Safe'
    END as note
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND proname LIKE '%credits%';

-- ============================================================================
-- 执行完成确认
-- ============================================================================

SELECT 
    '✅ 安全问题修复完成' as status,
    '已删除有风险的视图，索引功能正常' as description,
    now() as fixed_at;

-- ============================================================================
-- 使用说明
-- ============================================================================

/*
修复说明：
1. 删除了有安全风险的 credits_index_usage 视图
2. 保留了所有重要的索引（性能优化不受影响）
3. 提供了安全的直接查询方式来监控索引

如何监控索引使用情况：
1. 直接运行上面的查询语句，不使用视图
2. 在需要时手动执行 EXPLAIN ANALYZE 来测试性能
3. 定期检查索引大小和使用统计

性能优化效果：
- 所有索引仍然存在并正常工作
- 查询性能提升不受影响
- 只是移除了有安全风险的监控视图

安全最佳实践：
- 避免创建 SECURITY DEFINER 视图
- 使用直接查询而不是视图来监控
- 定期审查数据库对象的安全属性
*/
