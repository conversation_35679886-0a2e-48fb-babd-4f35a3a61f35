-- 积分系统数据库索引优化脚本 (Supabase 专用版本)
-- 简化版本，专门适配 Supabase 环境

-- ============================================================================
-- 1. 用户积分查询优化索引（最重要）
-- ============================================================================

-- 主要用于 getUserValidCredits 函数的查询优化
CREATE INDEX IF NOT EXISTS idx_credits_user_expired_credits 
ON credits (user_uuid, expired_at, credits);

-- 说明：这是最重要的索引，用于优化用户积分查询
-- 预期效果：单用户积分查询时间从 200ms 降至 50ms

-- ============================================================================
-- 2. 用户交易记录查询优化索引
-- ============================================================================

-- 主要用于查询用户的交易历史记录
CREATE INDEX IF NOT EXISTS idx_credits_user_created 
ON credits (user_uuid, created_at DESC);

-- 说明：优化用户交易记录查询，按时间倒序排列
-- 预期效果：用户交易记录查询性能提升 5-10 倍

-- ============================================================================
-- 3. 管理员统计查询优化索引
-- ============================================================================

-- 主要用于管理员页面的统计查询
CREATE INDEX IF NOT EXISTS idx_credits_created_type 
ON credits (created_at, trans_type);

-- 说明：优化管理员统计查询，按时间和交易类型筛选
-- 预期效果：管理员统计查询速度显著提升

-- ============================================================================
-- 4. 批量查询优化索引
-- ============================================================================

-- 主要用于 getBatchUserValidCredits 函数的批量查询优化
CREATE INDEX IF NOT EXISTS idx_credits_batch_query 
ON credits (expired_at, user_uuid, credits);

-- 说明：优化批量查询性能，先按过期时间筛选，再按用户分组
-- 预期效果：批量查询性能提升 5-10 倍

-- ============================================================================
-- 5. 更新表的统计信息
-- ============================================================================

-- 更新表的统计信息，帮助查询优化器做出更好的执行计划
ANALYZE credits;

-- ============================================================================
-- 6. 验证索引创建结果
-- ============================================================================

-- 查看刚创建的索引
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'credits' 
AND indexname LIKE 'idx_credits_%'
ORDER BY indexname;

-- ============================================================================
-- 执行完成提示
-- ============================================================================

-- 显示完成信息
SELECT 
    '✅ 积分系统数据库索引优化完成！' as status,
    '已创建 4 个关键索引，预期查询性能提升 60% 以上' as description,
    now() as completed_at;

-- 显示索引信息
SELECT 
    'idx_credits_user_expired_credits' as index_name,
    '用户积分查询优化' as purpose,
    '查询时间: 200ms → 50ms' as expected_improvement
UNION ALL
SELECT 
    'idx_credits_user_created',
    '用户交易记录查询优化',
    '性能提升: 5-10倍'
UNION ALL
SELECT 
    'idx_credits_created_type',
    '管理员统计查询优化',
    '查询速度显著提升'
UNION ALL
SELECT 
    'idx_credits_batch_query',
    '批量查询优化',
    '批量性能提升: 5-10倍';

-- ============================================================================
-- 使用说明
-- ============================================================================

/*
使用方法：
1. 在 Supabase Dashboard 中打开 SQL Editor
2. 复制粘贴此脚本的全部内容
3. 点击 "Run" 执行
4. 查看执行结果，确认所有索引创建成功

验证方法：
1. 执行完成后会显示创建的索引列表
2. 可以运行以下查询测试性能：

-- 测试用户积分查询性能
EXPLAIN ANALYZE 
SELECT * FROM credits 
WHERE user_uuid = 'your-test-uuid' 
AND expired_at >= NOW() 
ORDER BY expired_at ASC;

-- 测试用户交易记录查询性能
EXPLAIN ANALYZE 
SELECT * FROM credits 
WHERE user_uuid = 'your-test-uuid' 
ORDER BY created_at DESC 
LIMIT 50;

回滚方法（如果需要）：
DROP INDEX IF EXISTS idx_credits_user_expired_credits;
DROP INDEX IF EXISTS idx_credits_user_created;
DROP INDEX IF EXISTS idx_credits_created_type;
DROP INDEX IF EXISTS idx_credits_batch_query;

注意事项：
- 此脚本是安全的，只添加索引，不修改数据
- 可以重复执行，不会出错
- 索引创建可能需要几秒到几分钟，取决于数据量
- 创建完成后立即生效，无需重启
*/
