"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useCreditsOperations, CreditOperationPresets } from '@/hooks/useCreditsOperations';
import { Coins, Image, Zap, RotateCcw, Gift } from 'lucide-react';
import { toast } from 'sonner';

/**
 * 积分操作演示组件
 * 用于测试和演示乐观更新功能
 */
export default function CreditsOperationsDemo() {
  const {
    currentCredits,
    isPro,
    isRecharged,
    deductForImageGeneration,
    deductForApiCall,
    refundForFailedOperation,
    addGiftCredits,
    checkSufficientCredits,
    calculateImageGenerationCredits,
  } = useCreditsOperations();

  const [imageCount, setImageCount] = useState(1);
  const [highQuality, setHighQuality] = useState(false);
  const [apiCost, setApiCost] = useState(1);
  const [refundAmount, setRefundAmount] = useState(10);
  const [giftAmount, setGiftAmount] = useState(50);

  const handleImageGeneration = async () => {
    const requiredCredits = calculateImageGenerationCredits(imageCount, highQuality);
    
    if (!checkSufficientCredits(requiredCredits)) {
      toast.error(`积分不足！需要 ${requiredCredits} 积分，当前只有 ${currentCredits} 积分`);
      return;
    }

    const success = await deductForImageGeneration(
      imageCount, 
      highQuality, 
      CreditOperationPresets.verbose
    );

    if (success) {
      // 模拟图片生成过程
      setTimeout(() => {
        toast.success(`成功生成 ${imageCount} 张${highQuality ? '高质量' : '标准'}图片！`);
      }, 1000);
    }
  };

  const handleApiCall = async () => {
    if (!checkSufficientCredits(apiCost)) {
      toast.error(`积分不足！需要 ${apiCost} 积分，当前只有 ${currentCredits} 积分`);
      return;
    }

    const success = await deductForApiCall('ping', apiCost, CreditOperationPresets.verbose);

    if (success) {
      // 模拟API调用
      setTimeout(() => {
        toast.success('API调用成功！');
      }, 500);
    }
  };

  const handleRefund = async () => {
    const success = await refundForFailedOperation(
      refundAmount,
      `demo_transaction_${Date.now()}`,
      '演示退款操作',
      CreditOperationPresets.verbose
    );

    if (success) {
      toast.info('这是一个演示退款操作');
    }
  };

  const handleGift = async () => {
    const success = await addGiftCredits(
      giftAmount,
      '演示赠送积分',
      CreditOperationPresets.verbose
    );

    if (success) {
      toast.success('🎉 恭喜获得积分奖励！');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="w-5 h-5" />
            积分系统乐观更新演示
          </CardTitle>
          <CardDescription>
            演示积分操作的乐观更新机制，操作后UI立即响应，后台异步处理
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-500/10 p-4 rounded-lg border border-blue-500/20">
              <div className="text-sm text-blue-400 mb-1">当前积分</div>
              <div className="text-2xl font-bold text-blue-300">{currentCredits}</div>
            </div>
            <div className="bg-green-500/10 p-4 rounded-lg border border-green-500/20">
              <div className="text-sm text-green-400 mb-1">用户状态</div>
              <div className="text-lg font-semibold text-green-300">
                {isPro ? '付费用户' : '免费用户'}
              </div>
            </div>
            <div className="bg-purple-500/10 p-4 rounded-lg border border-purple-500/20">
              <div className="text-sm text-purple-400 mb-1">充值状态</div>
              <div className="text-lg font-semibold text-purple-300">
                {isRecharged ? '已充值' : '未充值'}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 图片生成操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Image className="w-4 h-4" />
                  图片生成
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="imageCount">图片数量</Label>
                  <Input
                    id="imageCount"
                    type="number"
                    min="1"
                    max="10"
                    value={imageCount}
                    onChange={(e) => setImageCount(parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="highQuality"
                    checked={highQuality}
                    onChange={(e) => setHighQuality(e.target.checked)}
                  />
                  <Label htmlFor="highQuality">高质量模式 (2x积分)</Label>
                </div>
                <div className="text-sm text-gray-400">
                  需要积分: {calculateImageGenerationCredits(imageCount, highQuality)}
                </div>
                <Button 
                  onClick={handleImageGeneration}
                  className="w-full"
                  disabled={!checkSufficientCredits(calculateImageGenerationCredits(imageCount, highQuality))}
                >
                  生成图片
                </Button>
              </CardContent>
            </Card>

            {/* API调用操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Zap className="w-4 h-4" />
                  API调用
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="apiCost">消耗积分</Label>
                  <Input
                    id="apiCost"
                    type="number"
                    min="1"
                    max="100"
                    value={apiCost}
                    onChange={(e) => setApiCost(parseInt(e.target.value) || 1)}
                  />
                </div>
                <Button 
                  onClick={handleApiCall}
                  className="w-full"
                  disabled={!checkSufficientCredits(apiCost)}
                >
                  调用API
                </Button>
              </CardContent>
            </Card>

            {/* 退款操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <RotateCcw className="w-4 h-4" />
                  退款操作
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="refundAmount">退款积分</Label>
                  <Input
                    id="refundAmount"
                    type="number"
                    min="1"
                    max="100"
                    value={refundAmount}
                    onChange={(e) => setRefundAmount(parseInt(e.target.value) || 10)}
                  />
                </div>
                <Button 
                  onClick={handleRefund}
                  className="w-full"
                  variant="outline"
                >
                  模拟退款
                </Button>
              </CardContent>
            </Card>

            {/* 赠送积分 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Gift className="w-4 h-4" />
                  积分奖励
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="giftAmount">奖励积分</Label>
                  <Input
                    id="giftAmount"
                    type="number"
                    min="1"
                    max="1000"
                    value={giftAmount}
                    onChange={(e) => setGiftAmount(parseInt(e.target.value) || 50)}
                  />
                </div>
                <Button 
                  onClick={handleGift}
                  className="w-full"
                  variant="secondary"
                >
                  获得奖励
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6 p-4 bg-gray-800/50 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">乐观更新说明</h3>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• 点击操作按钮后，积分显示立即更新</li>
              <li>• 后台异步执行实际的积分操作</li>
              <li>• 如果操作失败，UI会自动回滚到原始状态</li>
              <li>• 待处理操作期间，积分显示会有视觉提示</li>
              <li>• 30秒定期同步确保数据一致性</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
