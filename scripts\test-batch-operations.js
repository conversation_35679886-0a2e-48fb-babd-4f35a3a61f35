/**
 * 批量操作性能测试脚本
 * 模拟批量赠送积分的性能优化效果
 */

const { performance } = require('perf_hooks');

// 模拟批量操作管理器
class BatchOperationManager {
  constructor() {
    this.cache = new Map();
    this.operationStats = [];
    this.CACHE_TTL = 30 * 1000;
  }

  // 模拟单个用户查询（传统方式）
  async traditionalSingleUserOperation(userEmail, credits) {
    const startTime = performance.now();
    
    // 模拟用户查询
    await this.simulateUserLookup(userEmail);
    
    // 模拟积分查询
    await this.simulateCreditsQuery(userEmail);
    
    // 模拟积分增加
    await this.simulateCreditsIncrease(userEmail, credits);
    
    // 模拟缓存清除
    this.cache.delete(userEmail);
    
    const endTime = performance.now();
    return endTime - startTime;
  }

  // 模拟批量操作（优化方式）
  async optimizedBatchOperation(operations) {
    const startTime = performance.now();
    
    // 第一步：批量用户查询
    const userLookupStart = performance.now();
    const userEmails = operations.map(op => op.userEmail);
    await this.simulateBatchUserLookup(userEmails);
    const userLookupTime = performance.now() - userLookupStart;
    
    // 第二步：缓存预热
    const cacheWarmupStart = performance.now();
    await this.simulateCacheWarmup(userEmails);
    const cacheWarmupTime = performance.now() - cacheWarmupStart;
    
    // 第三步：并行执行积分操作
    const parallelOpsStart = performance.now();
    const operationPromises = operations.map(op => 
      this.simulateOptimizedCreditsOperation(op.userEmail, op.credits)
    );
    await Promise.all(operationPromises);
    const parallelOpsTime = performance.now() - parallelOpsStart;
    
    // 第四步：批量缓存更新
    const cacheUpdateStart = performance.now();
    await this.simulateBatchCacheUpdate(userEmails);
    const cacheUpdateTime = performance.now() - cacheUpdateStart;
    
    const totalTime = performance.now() - startTime;
    
    return {
      totalTime,
      userLookupTime,
      cacheWarmupTime,
      parallelOpsTime,
      cacheUpdateTime,
      operationsCount: operations.length
    };
  }

  // 模拟用户查询
  async simulateUserLookup(userEmail) {
    // 模拟数据库查询延迟
    await new Promise(resolve => setTimeout(resolve, 20 + Math.random() * 10));
  }

  // 模拟批量用户查询
  async simulateBatchUserLookup(userEmails) {
    // 批量查询比单个查询效率更高
    const baseTime = 30; // 基础查询时间
    const perUserTime = 2; // 每个用户的额外时间
    const totalTime = baseTime + userEmails.length * perUserTime;
    await new Promise(resolve => setTimeout(resolve, totalTime));
  }

  // 模拟积分查询
  async simulateCreditsQuery(userEmail) {
    const cached = this.cache.get(userEmail);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      // 缓存命中，快速返回
      await new Promise(resolve => setTimeout(resolve, 1));
      return cached.data;
    }
    
    // 缓存未命中，查询数据库
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 30));
    const credits = 100 + Math.floor(Math.random() * 200);
    
    this.cache.set(userEmail, {
      data: credits,
      timestamp: Date.now()
    });
    
    return credits;
  }

  // 模拟缓存预热
  async simulateCacheWarmup(userEmails) {
    // 批量预热比逐个查询效率更高
    const baseTime = 20;
    const perUserTime = 3;
    const totalTime = baseTime + userEmails.length * perUserTime;
    await new Promise(resolve => setTimeout(resolve, totalTime));
    
    // 预热缓存
    userEmails.forEach(email => {
      this.cache.set(email, {
        data: 100 + Math.floor(Math.random() * 200),
        timestamp: Date.now()
      });
    });
  }

  // 模拟积分增加
  async simulateCreditsIncrease(userEmail, credits) {
    // 模拟数据库写入
    await new Promise(resolve => setTimeout(resolve, 30 + Math.random() * 20));
  }

  // 模拟优化的积分操作
  async simulateOptimizedCreditsOperation(userEmail, credits) {
    // 由于缓存预热，查询很快
    await this.simulateCreditsQuery(userEmail);
    
    // 积分增加操作
    await this.simulateCreditsIncrease(userEmail, credits);
  }

  // 模拟批量缓存更新
  async simulateBatchCacheUpdate(userEmails) {
    // 批量更新缓存
    const baseTime = 10;
    const perUserTime = 1;
    const totalTime = baseTime + userEmails.length * perUserTime;
    await new Promise(resolve => setTimeout(resolve, totalTime));
    
    // 更新缓存
    userEmails.forEach(email => {
      const existing = this.cache.get(email);
      if (existing) {
        this.cache.set(email, {
          ...existing,
          timestamp: Date.now()
        });
      }
    });
  }

  // 重置状态
  reset() {
    this.cache.clear();
    this.operationStats = [];
  }
}

// 测试函数
async function testBatchOperations() {
  console.log('🚀 开始测试批量操作性能优化效果\n');
  
  const manager = new BatchOperationManager();
  
  // 测试数据
  const testOperations = [
    { userEmail: '<EMAIL>', credits: 100 },
    { userEmail: '<EMAIL>', credits: 150 },
    { userEmail: '<EMAIL>', credits: 200 },
    { userEmail: '<EMAIL>', credits: 120 },
    { userEmail: '<EMAIL>', credits: 180 },
    { userEmail: '<EMAIL>', credits: 90 },
    { userEmail: '<EMAIL>', credits: 250 },
    { userEmail: '<EMAIL>', credits: 110 },
    { userEmail: '<EMAIL>', credits: 160 },
    { userEmail: '<EMAIL>', credits: 140 },
  ];

  // 测试1: 传统方式 vs 批量优化方式
  console.log('📊 测试1: 传统方式 vs 批量优化方式');
  console.log('=' .repeat(50));
  
  // 传统方式：逐个处理
  console.log('传统方式测试（逐个处理）...');
  manager.reset();
  
  const traditionalStart = performance.now();
  const traditionalTimes = [];
  
  for (const operation of testOperations) {
    const operationTime = await manager.traditionalSingleUserOperation(
      operation.userEmail, 
      operation.credits
    );
    traditionalTimes.push(operationTime);
  }
  
  const traditionalTotal = performance.now() - traditionalStart;
  const traditionalAvg = traditionalTimes.reduce((sum, time) => sum + time, 0) / traditionalTimes.length;
  
  console.log(`传统方式结果:`);
  console.log(`  总时间: ${traditionalTotal.toFixed(2)}ms`);
  console.log(`  平均每操作: ${traditionalAvg.toFixed(2)}ms`);
  console.log(`  操作数量: ${testOperations.length}`);
  console.log();

  // 批量优化方式
  console.log('批量优化方式测试...');
  manager.reset();
  
  const optimizedResult = await manager.optimizedBatchOperation(testOperations);
  
  console.log(`批量优化结果:`);
  console.log(`  总时间: ${optimizedResult.totalTime.toFixed(2)}ms`);
  console.log(`  平均每操作: ${(optimizedResult.totalTime / optimizedResult.operationsCount).toFixed(2)}ms`);
  console.log(`  用户查询时间: ${optimizedResult.userLookupTime.toFixed(2)}ms`);
  console.log(`  缓存预热时间: ${optimizedResult.cacheWarmupTime.toFixed(2)}ms`);
  console.log(`  并行操作时间: ${optimizedResult.parallelOpsTime.toFixed(2)}ms`);
  console.log(`  缓存更新时间: ${optimizedResult.cacheUpdateTime.toFixed(2)}ms`);
  console.log();

  // 性能提升计算
  const timeImprovement = ((traditionalTotal - optimizedResult.totalTime) / traditionalTotal * 100).toFixed(2);
  const speedup = (traditionalTotal / optimizedResult.totalTime).toFixed(1);
  
  console.log('📈 性能提升对比:');
  console.log(`  执行时间减少: ${timeImprovement}%`);
  console.log(`  速度提升: ${speedup}x`);
  console.log();

  // 测试2: 不同规模的批量操作
  console.log('📊 测试2: 不同规模的批量操作性能');
  console.log('=' .repeat(50));
  
  const scales = [5, 10, 20, 50];
  
  for (const scale of scales) {
    const scaleOperations = Array.from({ length: scale }, (_, i) => ({
      userEmail: `user${i + 1}@example.com`,
      credits: 100 + Math.floor(Math.random() * 100)
    }));
    
    manager.reset();
    
    // 传统方式
    const traditionalScaleStart = performance.now();
    for (const operation of scaleOperations) {
      await manager.traditionalSingleUserOperation(operation.userEmail, operation.credits);
    }
    const traditionalScaleTime = performance.now() - traditionalScaleStart;
    
    manager.reset();
    
    // 批量方式
    const optimizedScaleResult = await manager.optimizedBatchOperation(scaleOperations);
    
    const scaleImprovement = ((traditionalScaleTime - optimizedScaleResult.totalTime) / traditionalScaleTime * 100).toFixed(2);
    
    console.log(`${scale} 个操作:`);
    console.log(`  传统方式: ${traditionalScaleTime.toFixed(2)}ms`);
    console.log(`  批量方式: ${optimizedScaleResult.totalTime.toFixed(2)}ms`);
    console.log(`  性能提升: ${scaleImprovement}%`);
    console.log();
  }

  // 测试3: 缓存预热效果
  console.log('📊 测试3: 缓存预热效果验证');
  console.log('=' .repeat(50));
  
  const cacheTestOperations = testOperations.slice(0, 5);
  
  // 无缓存预热
  manager.reset();
  const noCacheStart = performance.now();
  for (const operation of cacheTestOperations) {
    await manager.simulateCreditsQuery(operation.userEmail);
  }
  const noCacheTime = performance.now() - noCacheStart;
  
  // 有缓存预热
  manager.reset();
  const withCacheStart = performance.now();
  await manager.simulateCacheWarmup(cacheTestOperations.map(op => op.userEmail));
  for (const operation of cacheTestOperations) {
    await manager.simulateCreditsQuery(operation.userEmail);
  }
  const withCacheTime = performance.now() - withCacheStart;
  
  const cacheImprovement = ((noCacheTime - withCacheTime) / noCacheTime * 100).toFixed(2);
  
  console.log(`缓存预热效果:`);
  console.log(`  无预热查询时间: ${noCacheTime.toFixed(2)}ms`);
  console.log(`  有预热查询时间: ${withCacheTime.toFixed(2)}ms`);
  console.log(`  查询性能提升: ${cacheImprovement}%`);
  console.log();

  // 验收标准检查
  console.log('✅ 验收标准检查:');
  console.log('=' .repeat(50));
  
  const targetImprovement = 70; // 目标提升70%
  const actualImprovement = parseFloat(timeImprovement);
  
  console.log(`目标批量操作效率提升: ${targetImprovement}%`);
  console.log(`实际批量操作效率提升: ${actualImprovement}%`);
  console.log(`✅ 批量操作效率达标: ${actualImprovement >= targetImprovement ? '是' : '否'}`);
  
  const avgOperationTime = optimizedResult.totalTime / optimizedResult.operationsCount;
  const targetAvgTime = 50; // 目标平均每操作时间 < 50ms
  
  console.log(`目标平均操作时间: < ${targetAvgTime}ms`);
  console.log(`实际平均操作时间: ${avgOperationTime.toFixed(2)}ms`);
  console.log(`✅ 操作时间达标: ${avgOperationTime < targetAvgTime ? '是' : '否'}`);
  
  console.log('\n🎉 批量操作性能测试完成！');
  
  // 优化建议
  console.log('\n💡 批量操作优化建议:');
  console.log('=' .repeat(50));
  console.log('1. 使用批量用户查询减少数据库往返');
  console.log('2. 实施缓存预热机制提升查询性能');
  console.log('3. 并行处理积分操作提高吞吐量');
  console.log('4. 批量更新缓存而非逐个清除');
  console.log('5. 监控批量操作性能指标');
}

// 运行测试
testBatchOperations().catch(console.error);
