"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { GenerationResult, ImageData, GenerationSettings, ImageSlotStatus } from "@/types/image-results";
import ImageGrid from "./ImageGrid";
import ImagePreviewModal from "./ImagePreviewModal";
import { getUuid } from "@/lib/hash";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Settings, Type, Image as ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";

// 生成参数接口
interface GenerationParams {
  prompt: string;
  settings: GenerationSettings;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string;
}

interface ImageResultContainerProps {
  // 展示模式：传入完整的GenerationResult
  result?: GenerationResult;
  // 生成模式：传入生成参数，组件自行处理生成流程
  generationParams?: GenerationParams;
  className?: string;
  enableCountdown?: boolean;
  onCancel?: () => void;
  onGenerationComplete?: () => void;
  translations?: {
    status?: {
      waiting?: string;
      countdown?: string;
      generating?: string;
      completed?: string;
      failed?: string;
      preparing?: string;
    };
    messages?: {
      countdown_seconds?: string;
      slot_number?: string;
      retry?: string;
      generation_error?: string;
      generation_failed?: string;
    };
    time_format?: {
      month?: string;
      day?: string;
      hour?: string;
      minute?: string;
    };
  };
}

export default function ImageResultContainer({
  result: externalResult,
  generationParams,
  className,
  translations,
  enableCountdown = true,
  onCancel,
  onGenerationComplete,
}: ImageResultContainerProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);

  // 内部状态管理（用于生成模式）
  const [internalResult, setInternalResult] = useState<GenerationResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const generationTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 创建占位图片数据
  const createPlaceholderImages = (imageCount?: string): ImageData[] => {
    // 从设置中获取图片数量，默认为4
    const count = imageCount ? parseInt(imageCount) : 4;
    const validCount = Math.max(1, Math.min(4, count)); // 确保在1-4范围内

    return Array.from({ length: validCount }, (_, index) => ({
      url: `data:image/svg+xml;base64,${btoa(`
        <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
          <rect width="512" height="512" fill="#f3f4f6"/>
          <text x="256" y="256" text-anchor="middle" dy="0.3em" font-family="Arial, sans-serif" font-size="24" fill="#9ca3af">
            Generated Image ${index + 1}
          </text>
        </svg>
      `)}`,
      filename: `generated-image-${index + 1}.svg`,
      width: 512,
      height: 512,
    }));
  };

  // 倒计时逻辑
  const runCountdown = async (result: GenerationResult, imageIndex: number): Promise<void> => {
    return new Promise((resolve) => {
      let countdown = 5;

      // 更新当前图片为倒计时状态
      setInternalResult(prev => prev ? {
        ...prev,
        currentGeneratingIndex: imageIndex,
        imageSlots: prev.imageSlots?.map(slot =>
          slot.index === imageIndex
            ? { ...slot, status: 'countdown', countdown, isActive: true }
            : { ...slot, isActive: false }
        ) || []
      } : null);

      const timer = setInterval(() => {
        countdown--;

        if (countdown <= 0) {
          clearInterval(timer);
          resolve();
        } else {
          setInternalResult(prev => prev ? {
            ...prev,
            imageSlots: prev.imageSlots?.map(slot =>
              slot.index === imageIndex
                ? { ...slot, countdown }
                : slot
            ) || []
          } : null);
        }
      }, 1000);

      countdownTimerRef.current = timer;
    });
  };

  // 生成单个图片
  const generateSingleImage = async (result: GenerationResult, imageIndex: number, placeholderImage: ImageData): Promise<void> => {
    // 更新为生成中状态
    setInternalResult(prev => prev ? {
      ...prev,
      imageSlots: prev.imageSlots?.map(slot =>
        slot.index === imageIndex
          ? { ...slot, status: 'generating', countdown: 0 }
          : slot
      ) || []
    } : null);

    // 模拟生成时间（3秒）
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 完成生成，添加图片
    setInternalResult(prev => prev ? {
      ...prev,
      images: [...prev.images, placeholderImage],
      imageSlots: prev.imageSlots?.map(slot =>
        slot.index === imageIndex
          ? { ...slot, status: 'completed', image: placeholderImage }
          : slot
      ) || []
    } : null);
  };

  // 处理图片生成流程
  const processImageGeneration = async (result: GenerationResult, placeholderImages: ImageData[]) => {
    // 模拟积分扣除 - 在开始生成前扣除积分
    try {
      const imageCount = result.settings.imageCount ? parseInt(result.settings.imageCount) : 4;
      const validImageCount = Math.max(1, Math.min(4, imageCount));
      const highQuality = result.settings.enableHighQuality || false;

      // 计算所需积分 (基础1积分 × 图片数量 × 质量倍数)
      const baseCredits = 1;
      const qualityMultiplier = highQuality ? 2 : 1;
      const requiredCredits = baseCredits * validImageCount * qualityMultiplier;

      console.log(`[占位图生成] 模拟积分扣除: ${requiredCredits} 积分 (${validImageCount} 图片 × ${qualityMultiplier} 质量倍数)`);

      // 模拟调用积分扣除API
      const deductResponse = await fetch('/api/user/credits/deduct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transType: 'image_generation',
          credits: requiredCredits,
          description: `占位图生成: ${validImageCount} 图片, ${highQuality ? '高质量' : '标准质量'}`,
        }),
      });

      if (!deductResponse.ok) {
        const errorData = await deductResponse.json();
        console.error('[占位图生成] 积分扣除失败:', errorData);

        // 积分不足或扣除失败，停止生成
        setInternalResult(prev => prev ? {
          ...prev,
          status: 'failed',
          currentGeneratingIndex: undefined,
        } : null);
        setIsGenerating(false);

        // 通知父组件生成完成（失败也需要重置按钮状态）
        console.log('[ImageResultContainer] 生成失败，通知父组件重置按钮状态');
        onGenerationComplete?.();
        return;
      }

      const deductResult = await deductResponse.json();
      console.log('[占位图生成] 积分扣除成功:', deductResult);

    } catch (error) {
      console.error('[占位图生成] 积分扣除异常:', error);

      // 扣除异常，停止生成
      setInternalResult(prev => prev ? {
        ...prev,
        status: 'failed',
        currentGeneratingIndex: undefined,
      } : null);
      setIsGenerating(false);

      // 通知父组件生成完成（失败也需要重置按钮状态）
      console.log('[ImageResultContainer] 生成异常，通知父组件重置按钮状态');
      onGenerationComplete?.();
      return;
    }

    // 积分扣除成功，继续生成流程
    const imageCount = result.settings.imageCount ? parseInt(result.settings.imageCount) : 4;
    const validImageCount = Math.max(1, Math.min(4, imageCount));

    for (let imageIndex = 0; imageIndex < validImageCount; imageIndex++) {
      // 倒计时阶段
      await runCountdown(result, imageIndex);

      // 生成阶段
      await generateSingleImage(result, imageIndex, placeholderImages[imageIndex]);

      // 小延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 完成所有生成
    setInternalResult(prev => prev ? {
      ...prev,
      status: 'completed',
      currentGeneratingIndex: undefined,
    } : null);
    setIsGenerating(false);

    // 通知父组件生成完成
    console.log('[ImageResultContainer] 所有图片生成完成，通知父组件');
    onGenerationComplete?.();
  };

  // 开始生成流程
  const startGeneration = useCallback(async (params: GenerationParams) => {
    const placeholderImages = createPlaceholderImages(params.settings.imageCount);

    // 获取图片数量
    const imageCount = params.settings.imageCount ? parseInt(params.settings.imageCount) : 4;
    const validImageCount = Math.max(1, Math.min(4, imageCount));

    // 创建初始的GenerationResult
    const initialResult: GenerationResult = {
      id: getUuid(),
      timestamp: new Date(),
      prompt: params.prompt,
      settings: params.settings,
      images: [],
      status: 'generating',
      mode: params.mode,
      uploadedImage: params.uploadedImage,
      imageSlots: Array.from({ length: validImageCount }, (_, index) => ({
        index,
        status: index === 0 ? 'countdown' : 'waiting',
        countdown: index === 0 ? 5 : undefined,
      })),
      currentGeneratingIndex: 0,
    };

    setInternalResult(initialResult);
    setIsGenerating(true);

    // 开始倒计时和生成流程
    await processImageGeneration(initialResult, placeholderImages);
  }, []);

  // 处理generationParams，开始生成流程
  useEffect(() => {
    if (generationParams && !externalResult && !internalResult) {
      startGeneration(generationParams);
    }
  }, [generationParams, externalResult, internalResult, startGeneration]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
      }
      if (generationTimerRef.current) {
        clearInterval(generationTimerRef.current);
      }
    };
  }, []);

  // 决定使用外部传入的result还是内部生成的result
  const result = externalResult || internalResult;

  // 如果没有result且没有generationParams，不渲染任何内容
  if (!result && !generationParams) {
    return null;
  }

  // 如果有generationParams但还没有result，显示加载状态
  if (!result && generationParams) {
    return (
      <div className={cn(
        "bg-gradient-to-br from-gray-900 to-black rounded-xl border border-white/20 shadow-2xl",
        className
      )}>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white/70">正在初始化生成...</p>
        </div>
      </div>
    );
  }

  const handleImageClick = (image: ImageData, index: number) => {
    setPreviewIndex(index);
    setPreviewOpen(true);
  };

  const formatTimestamp = (timestamp: Date) => {
    // 检测当前语言环境 - 修复语言检测逻辑
    // 默认语言（英文）路径是 /，中文路径是 /zh
    const locale = typeof window !== 'undefined' && window.location.pathname.startsWith('/zh') ? 'zh-CN' : 'en-US';

    if (locale === 'en-US') {
      // 英文格式：Jan 3, 6:05 PM
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      }).format(timestamp);
    } else {
      // 中文格式：1月3日 18:05
      return new Intl.DateTimeFormat('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(timestamp);
    }
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };







  const getStatusBadge = () => {
    switch (result.status) {
      case 'waiting':
        return <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-300">{translations?.status?.waiting || '等待中'}</Badge>;
      case 'generating':
        return <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">{translations?.status?.generating || '生成中'}</Badge>;
      case 'completed':
        return <Badge variant="secondary" className="bg-green-500/20 text-green-300">{translations?.status?.completed || '已完成'}</Badge>;
      case 'failed':
        return <Badge variant="destructive" className="bg-red-500/20 text-red-300">{translations?.status?.failed || '生成失败'}</Badge>;
      default:
        return null;
    }
  };



  return (
    <div className={cn(
      "w-full border border-white/20 rounded-2xl overflow-hidden bg-black/40 backdrop-blur-xl",
      className
    )} data-image-result-container>
      {/* Header */}
      <div className="p-4 border-b border-white/20 bg-black/10">
        <div className="flex items-start justify-between gap-4">
          {/* Left side - Generation info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {result.mode === 'text-to-image' ? (
                <Type className="w-4 h-4 text-white/70" />
              ) : (
                <ImageIcon className="w-4 h-4 text-white/70" />
              )}
              <span className="text-white/70 text-sm">
                {result.mode === 'text-to-image'
                  ? (translations?.messages?.text_to_image || '文本生成图片')
                  : (translations?.messages?.image_to_image || '图片转图片')
                }
              </span>
              {getStatusBadge()}
            </div>
            
            <div className="space-y-1">
              <p className="text-white text-sm font-medium">
                {truncateText(result.prompt)}
              </p>
              
              {/* Settings display */}
              {Object.keys(result.settings).length > 0 && (
                <div className="flex items-center gap-2 text-white/60 text-xs">
                  <Settings className="w-3 h-3" />
                  <span>
                    {result.settings.ratio && `${result.settings.ratio} • `}
                    {result.settings.style && `${result.settings.style} • `}
                    {result.settings.color && `${result.settings.color} • `}
                    {result.settings.lighting && `${result.settings.lighting} • `}
                    {result.settings.composition && `${result.settings.composition} • `}
                    {result.settings.enableHighQuality && (translations?.messages?.high_quality || '高质量')}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Right side - Timestamp and actions */}
          <div className="flex flex-col items-end gap-2">
            <div className="flex items-center gap-1 text-white/60 text-xs">
              <Calendar className="w-3 h-3" />
              <span>{formatTimestamp(result.timestamp)}</span>
            </div>
            

          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">


        {/* Show error for failed status - 整体生成失败时不显示网格 */}
        {result.status === 'failed' ? (
          <div className="text-center py-8">
            <div className="text-red-400 text-4xl mb-2">⚠️</div>
            <h3 className="text-white font-medium mb-1">{translations?.status?.failed || '生成失败'}</h3>
            <p className="text-white/60 text-sm">
              {result.error || (translations?.messages?.generation_error || '图片生成过程中出现错误，请重试')}
            </p>
          </div>
        ) : (
          /* 只有非失败状态才显示 ImageGrid */
          <ImageGrid
            images={result.images}
            onImageClick={handleImageClick}
            translations={translations}
            imageSlots={result.imageSlots}
          />
        )}


      </div>

      {/* Preview Modal */}
      {result.images.length > 0 && (
        <ImagePreviewModal
          isOpen={previewOpen}
          onClose={() => setPreviewOpen(false)}
          images={result.images}
          currentIndex={previewIndex}
          onIndexChange={setPreviewIndex}
        />
      )}
    </div>
  );
}
