import { useMemo } from "react";
import { SSRGalleryProps, ImageDataSSR } from "@/types/blocks/gallery";
import { cn } from "@/lib/utils";
import dynamic from "next/dynamic";

// 加载骨架屏组件
function GalleryLoadingSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array.from({ length: 12 }).map((_, index) => (
        <div
          key={index}
          className="bg-gradient-to-br from-muted to-muted/50 animate-pulse rounded-lg"
          style={{
            aspectRatio: 0.75,
            minHeight: "200px",
          }}
        />
      ))}
    </div>
  );
}

// 动态导入客户端组件，禁用SSR
const ClientGallery = dynamic(() => import("./ClientGallery"), {
  ssr: false,
  loading: () => <GalleryLoadingSkeleton />
});

// 模拟图片数据 - SSR版本（不包含src）
const mockImagesSSR: ImageDataSSR[] = [
  {
    id: "1",
    alt: "AI Generated Portrait 1",
    width: 512,
    height: 768,
    aspectRatio: 512 / 768,
    style: "Anime Style",
    color: "Vibrant Colors",
    lighting: "Soft Lighting",
    composition: "Portrait",
    prompt: "A beautiful anime character with vibrant colors, soft lighting, portrait composition",
    tags: ["anime", "portrait", "vibrant", "soft lighting"]
  },
  {
    id: "2",
    alt: "AI Generated Landscape 1",
    width: 512,
    height: 640,
    aspectRatio: 512 / 640,
    style: "Realistic Style",
    color: "Natural Colors",
    lighting: "Golden Hour",
    composition: "Landscape",
    prompt: "A stunning landscape with natural colors during golden hour",
    tags: ["landscape", "realistic", "golden hour", "nature"]
  },
  {
    id: "3",
    alt: "AI Generated Character 1",
    width: 512,
    height: 720,
    aspectRatio: 512 / 720,
    style: "Cartoon Style",
    color: "Pastel Tones",
    lighting: "Ambient Light",
    composition: "Full Body",
    prompt: "A cute cartoon character with pastel tones and ambient lighting",
    tags: ["cartoon", "character", "pastel", "cute"]
  },
  {
    id: "4",
    alt: "AI Generated Art 1",
    width: 512,
    height: 800,
    aspectRatio: 512 / 800,
    style: "Abstract Style",
    color: "Dark Palette",
    lighting: "Dramatic Light",
    composition: "Abstract",
    prompt: "An abstract artwork with dark palette and dramatic lighting",
    tags: ["abstract", "dark", "dramatic", "artistic"]
  },
  {
    id: "5",
    alt: "AI Generated Fantasy 1",
    width: 512,
    height: 680,
    aspectRatio: 512 / 680,
    style: "Fantasy Style",
    color: "Magical Colors",
    lighting: "Mystical Light",
    composition: "Fantasy Scene",
    prompt: "A magical fantasy scene with mystical lighting and enchanting colors",
    tags: ["fantasy", "magical", "mystical", "enchanting"]
  },
  {
    id: "6",
    alt: "AI Generated Sci-Fi 1",
    width: 512,
    height: 750,
    aspectRatio: 512 / 750,
    style: "Sci-Fi Style",
    color: "Neon Colors",
    lighting: "Futuristic Light",
    composition: "Sci-Fi Scene",
    prompt: "A futuristic sci-fi scene with neon colors and advanced lighting",
    tags: ["sci-fi", "futuristic", "neon", "advanced"]
  }
];

export default function SSRGallery({ gallery, className }: SSRGalleryProps) {
  // 使用传入的图片数据或模拟数据
  const images = useMemo(() => {
    if (gallery.images && gallery.images.length > 0) {
      return gallery.images;
    } else {
      // 使用模拟数据，生成16张图片用于演示
      return Array.from({ length: 16 }, (_, index) => ({
        ...mockImagesSSR[index % mockImagesSSR.length],
        id: `${index + 1}`,
      }));
    }
  }, [gallery.images]);

  if (images.length === 0) {
    return (
      <section className={cn("py-8 lg:py-16", className)}>
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center text-brand-dark">
            <p>{gallery.translations?.no_data || "暂无图片"}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={cn("py-8 lg:py-16", className)}>
      <div className="container mx-auto px-4 lg:px-8">
        {/* 标题区域 - SSR渲染 */}
        {(gallery.title || gallery.subtitle) && (
          <div className="text-center mb-12">
            {gallery.title && (
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white drop-shadow-lg">
                {gallery.title}
              </h2>
            )}
            {gallery.subtitle && (
              <p className="text-lg max-w-2xl mx-auto" style={{color: '#475569'}}>
                {gallery.subtitle}
              </p>
            )}
          </div>
        )}

        {/* 客户端图片加载组件 */}
        <ClientGallery
          images={images}
          translations={gallery.translations}
        />
      </div>
    </section>
  );
}
