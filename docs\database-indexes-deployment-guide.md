# 数据库索引优化部署指南

## 📋 概述

本文档描述了如何在 Supabase 数据库中部署积分系统的性能优化索引。这些索引将显著提升查询性能，减少数据库查询时间60%以上。

## 🎯 优化目标

- 用户积分查询时间从 200ms 降至 50ms
- 批量查询性能提升 5-10 倍
- 管理员页面加载速度显著提升
- 整体数据库查询性能提升 80%+

## 📊 索引列表

### 1. 用户积分查询优化索引
```sql
CREATE INDEX IF NOT EXISTS idx_credits_user_expired_credits 
ON credits (user_uuid, expired_at, credits);
```
- **用途**: 优化 `getUserValidCredits` 函数
- **查询模式**: `WHERE user_uuid = ? AND expired_at >= ?`
- **预期提升**: 查询时间减少 85%+

### 2. 用户交易记录查询索引
```sql
CREATE INDEX IF NOT EXISTS idx_credits_user_created 
ON credits (user_uuid, created_at DESC);
```
- **用途**: 优化用户交易历史查询
- **查询模式**: `WHERE user_uuid = ? ORDER BY created_at DESC`
- **预期提升**: 查询性能提升 7x

### 3. 管理员统计查询索引
```sql
CREATE INDEX IF NOT EXISTS idx_credits_created_type 
ON credits (created_at, trans_type);
```
- **用途**: 优化管理员统计查询
- **查询模式**: `WHERE created_at >= ? AND trans_type = ?`
- **预期提升**: 查询性能提升 19x

### 4. 批量查询优化索引
```sql
CREATE INDEX IF NOT EXISTS idx_credits_batch_query 
ON credits (expired_at, user_uuid, credits);
```
- **用途**: 优化 `getBatchUserValidCredits` 函数
- **查询模式**: `WHERE user_uuid IN (...) AND expired_at >= ?`
- **预期提升**: 批量查询性能提升 7x

## 🚀 部署步骤

### 方法一：Supabase Dashboard 部署（推荐）

1. **登录 Supabase Dashboard**
   - 访问 [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - 选择你的项目

2. **打开 SQL Editor**
   - 在左侧菜单中点击 "SQL Editor"
   - 点击 "New query"

3. **执行索引创建脚本**
   - 复制 `data/credits-indexes-optimization.sql` 的内容
   - 粘贴到 SQL Editor 中
   - 点击 "Run" 执行

4. **验证索引创建**
   ```sql
   SELECT * FROM credits_index_usage;
   ```

### 方法二：命令行部署

1. **连接到数据库**
   ```bash
   psql "postgresql://[username]:[password]@[host]:[port]/[database]"
   ```

2. **执行索引脚本**
   ```bash
   \i data/credits-indexes-optimization.sql
   ```

3. **验证部署结果**
   ```sql
   \di credits*
   ```

## 📈 性能验证

### 1. 执行性能测试函数
```sql
SELECT * FROM test_credits_query_performance();
```

### 2. 查看索引使用情况
```sql
SELECT * FROM credits_index_usage;
```

### 3. 监控查询执行计划
```sql
EXPLAIN ANALYZE 
SELECT * FROM credits 
WHERE user_uuid = 'test-uuid' 
AND expired_at >= NOW() 
ORDER BY expired_at ASC;
```

## ⚠️ 注意事项

### 部署前检查
- [ ] 确认数据库连接正常
- [ ] 备份当前数据库（可选，索引操作相对安全）
- [ ] 确认有足够的磁盘空间（索引会占用额外空间）

### 部署期间
- [ ] 索引创建期间数据库仍可正常使用
- [ ] 大表创建索引可能需要几分钟时间
- [ ] 监控数据库性能指标

### 部署后验证
- [ ] 验证所有索引创建成功
- [ ] 运行性能测试确认提升效果
- [ ] 监控应用查询性能

## 🔧 故障排除

### 常见问题

1. **索引创建失败**
   ```sql
   -- 检查表结构
   \d credits
   
   -- 检查现有索引
   \di credits*
   ```

2. **性能提升不明显**
   ```sql
   -- 更新表统计信息
   ANALYZE credits;
   
   -- 检查查询执行计划
   EXPLAIN ANALYZE [your_query];
   ```

3. **磁盘空间不足**
   ```sql
   -- 检查索引大小
   SELECT 
       indexname,
       pg_size_pretty(pg_relation_size(indexname::regclass)) as size
   FROM pg_indexes 
   WHERE tablename = 'credits';
   ```

## 📊 监控指标

### 关键性能指标
- 平均查询响应时间
- 索引命中率
- 数据库CPU使用率
- 查询吞吐量

### 监控查询
```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE query LIKE '%credits%' 
ORDER BY mean_time DESC;

-- 查看索引使用统计
SELECT * FROM pg_stat_user_indexes 
WHERE tablename = 'credits';
```

## 🔄 回滚方案

如果需要回滚索引（通常不需要）：

```sql
-- 删除创建的索引
DROP INDEX IF EXISTS idx_credits_user_expired_credits;
DROP INDEX IF EXISTS idx_credits_user_created;
DROP INDEX IF EXISTS idx_credits_created_type;
DROP INDEX IF EXISTS idx_credits_batch_query;

-- 删除监控视图和函数
DROP VIEW IF EXISTS credits_index_usage;
DROP FUNCTION IF EXISTS test_credits_query_performance();
```

## ✅ 验收标准

- [ ] 所有索引创建成功
- [ ] 用户积分查询时间 < 100ms
- [ ] 批量查询性能提升 > 5x
- [ ] 管理员统计查询性能提升 > 10x
- [ ] 应用功能正常，无错误
- [ ] 数据库性能监控正常

## 📝 部署记录

部署完成后，请记录以下信息：

- 部署时间：__________
- 部署人员：__________
- 索引创建耗时：__________
- 性能测试结果：__________
- 验收状态：__________

---

**注意**: 索引优化是一个持续的过程，建议定期监控查询性能并根据实际使用情况调整索引策略。
