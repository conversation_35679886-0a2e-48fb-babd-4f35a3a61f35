"use client";

import { useState, useEffect, useMemo } from "react";
import { ClientGalleryProps, ImageDataSSR, ImageDataCSR, ImageLoadState } from "@/types/blocks/gallery";
import ImageCardSSR from "./ImageCardSSR";
import ImageModal from "./ImageModal";
import { useMediaQuery } from "@/hooks/useMediaQuery";

export default function ClientGallery({ images, translations, onImageClick }: ClientGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<ImageDataCSR | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imageLoadStates, setImageLoadStates] = useState<ImageLoadState[]>([]);
  const [isPageFullyLoaded, setIsPageFullyLoaded] = useState(false);

  // 响应式断点检测
  const isMobile = useMediaQuery('(max-width: 767px)');     // 移动端：2列
  const isTablet = useMediaQuery('(max-width: 1023px)');   // 平板端：3列
  // 桌面端：4列

  // 检测页面完全加载
  useEffect(() => {
    const checkPageLoaded = () => {
      if (document.readyState === 'complete') {
        // 添加额外延迟确保所有资源加载完成
        setTimeout(() => {
          setIsPageFullyLoaded(true);
        }, 1000);
      }
    };

    // 立即检查
    checkPageLoaded();

    // 监听状态变化
    const handleReadyStateChange = () => {
      checkPageLoaded();
    };

    // 监听window load事件
    const handleWindowLoad = () => {
      setTimeout(() => {
        setIsPageFullyLoaded(true);
      }, 1000);
    };

    document.addEventListener('readystatechange', handleReadyStateChange);
    window.addEventListener('load', handleWindowLoad);

    return () => {
      document.removeEventListener('readystatechange', handleReadyStateChange);
      window.removeEventListener('load', handleWindowLoad);
    };
  }, []);

  // 初始化图片加载状态
  useEffect(() => {
    const initialStates: ImageLoadState[] = images.map(image => ({
      id: image.id,
      isLoaded: false,
      hasError: false,
      src: undefined
    }));
    setImageLoadStates(initialStates);
  }, [images]);

  // 页面完全加载后，延迟设置图片src
  useEffect(() => {
    if (isPageFullyLoaded && imageLoadStates.length > 0) {
      const updateImageSources = () => {
        setImageLoadStates(prevStates => 
          prevStates.map(state => ({
            ...state,
            src: `/imgs/showcases/${((parseInt(state.id) - 1) % 9) + 1}.png`
          }))
        );
      };

      // 再次延迟确保页面稳定
      setTimeout(updateImageSources, 500);
    }
  }, [isPageFullyLoaded, imageLoadStates.length]);

  // 响应式瀑布流布局计算
  const columns = useMemo(() => {
    // 根据屏幕大小动态决定列数
    const getColumnCount = () => {
      if (isMobile) return 2;      // 移动端：2列
      if (isTablet) return 3;      // 平板端：3列
      return 4;                    // 桌面端：4列
    };

    const columnCount = getColumnCount();

    // 为每列创建数组
    const cols: ImageDataSSR[][] = Array.from({ length: columnCount }, () => []);
    const colHeights = Array(columnCount).fill(0);

    // 将图片分配到高度最小的列
    images.forEach((image) => {
      const shortestColIndex = colHeights.indexOf(Math.min(...colHeights));
      cols[shortestColIndex].push(image);
      colHeights[shortestColIndex] += image.aspectRatio ? (1 / image.aspectRatio) : 1;
    });

    return cols;
  }, [images, isMobile, isTablet]);

  const handleImageClick = (image: ImageDataSSR) => {
    // 找到对应的加载状态
    const loadState = imageLoadStates.find(state => state.id === image.id);
    if (loadState && loadState.src) {
      const fullImage: ImageDataCSR = {
        ...image,
        src: loadState.src
      };
      setSelectedImage(fullImage);
      setIsModalOpen(true);
      
      // 调用外部回调
      if (onImageClick) {
        onImageClick(fullImage);
      }
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setTimeout(() => setSelectedImage(null), 300);
  };

  const updateImageLoadState = (id: string, updates: Partial<ImageLoadState>) => {
    setImageLoadStates(prevStates =>
      prevStates.map(state =>
        state.id === id ? { ...state, ...updates } : state
      )
    );
  };

  if (images.length === 0) {
    return (
      <div className="text-center text-brand-dark">
        <p>{translations?.no_data || "暂无图片"}</p>
      </div>
    );
  }

  return (
    <>
      {/* 响应式瀑布流网格 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {columns.map((column, columnIndex) => (
          <div key={columnIndex} className="flex flex-col gap-4">
            {column.map((image) => {
              const loadState = imageLoadStates.find(state => state.id === image.id);
              return (
                <ImageCardSSR
                  key={image.id}
                  image={image}
                  loadState={loadState}
                  onClick={() => handleImageClick(image)}
                  onLoadStateChange={updateImageLoadState}
                  className="w-full"
                />
              );
            })}
          </div>
        ))}
      </div>

      {/* 图片详情模态框 */}
      <ImageModal
        image={selectedImage}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        translations={translations}
      />
    </>
  );
}
