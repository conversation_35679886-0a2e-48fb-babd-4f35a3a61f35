/**
 * 前端乐观更新机制测试脚本
 * 模拟前端乐观更新的逻辑和性能
 */

const { performance } = require('perf_hooks');

// 模拟前端状态管理
class OptimisticCreditsManager {
  constructor(initialCredits = 100) {
    this.credits = initialCredits;
    this.pendingOperations = [];
    this.operationHistory = [];
  }

  // 传统方式：等待服务器响应
  async traditionalDeduct(amount, apiDelay = 200) {
    const startTime = performance.now();
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, apiDelay));
    
    // 模拟成功率（90%）
    const success = Math.random() > 0.1;
    
    if (success) {
      this.credits -= amount;
    }
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    this.operationHistory.push({
      type: 'traditional',
      amount,
      success,
      responseTime,
      userWaitTime: responseTime // 用户需要等待整个响应时间
    });
    
    return { success, responseTime };
  }

  // 乐观更新方式：立即更新UI
  async optimisticDeduct(amount, apiDelay = 200) {
    const startTime = performance.now();
    const operationId = `op_${Date.now()}_${Math.random()}`;
    
    // 检查积分是否足够
    if (this.credits < amount) {
      return { success: false, responseTime: 0, userWaitTime: 0 };
    }
    
    // 立即更新UI（用户感知的响应时间几乎为0）
    const originalCredits = this.credits;
    this.credits -= amount;
    const uiUpdateTime = performance.now();
    const userWaitTime = uiUpdateTime - startTime; // 用户感知的等待时间
    
    // 添加到待处理操作
    this.pendingOperations.push({ id: operationId, amount, originalCredits });
    
    // 后台异步处理
    const apiPromise = new Promise(resolve => setTimeout(resolve, apiDelay))
      .then(() => {
        // 模拟成功率（90%）
        const success = Math.random() > 0.1;
        return success;
      });
    
    try {
      const success = await apiPromise;
      const endTime = performance.now();
      const totalResponseTime = endTime - startTime;
      
      // 移除待处理操作
      this.pendingOperations = this.pendingOperations.filter(op => op.id !== operationId);
      
      if (!success) {
        // 失败：回滚UI状态
        this.credits = originalCredits;
      }
      
      this.operationHistory.push({
        type: 'optimistic',
        amount,
        success,
        responseTime: totalResponseTime,
        userWaitTime // 用户实际等待时间（几乎为0）
      });
      
      return { success, responseTime: totalResponseTime, userWaitTime };
      
    } catch (error) {
      // 错误处理：回滚UI状态
      this.credits = originalCredits;
      this.pendingOperations = this.pendingOperations.filter(op => op.id !== operationId);
      
      return { success: false, responseTime: performance.now() - startTime, userWaitTime };
    }
  }

  getStats() {
    const traditional = this.operationHistory.filter(op => op.type === 'traditional');
    const optimistic = this.operationHistory.filter(op => op.type === 'optimistic');
    
    const avgTraditionalWait = traditional.length > 0 
      ? traditional.reduce((sum, op) => sum + op.userWaitTime, 0) / traditional.length 
      : 0;
      
    const avgOptimisticWait = optimistic.length > 0 
      ? optimistic.reduce((sum, op) => sum + op.userWaitTime, 0) / optimistic.length 
      : 0;
    
    const traditionalSuccess = traditional.filter(op => op.success).length;
    const optimisticSuccess = optimistic.filter(op => op.success).length;
    
    return {
      traditional: {
        count: traditional.length,
        successRate: traditional.length > 0 ? (traditionalSuccess / traditional.length * 100).toFixed(2) : '0.00',
        avgUserWaitTime: avgTraditionalWait.toFixed(2),
      },
      optimistic: {
        count: optimistic.length,
        successRate: optimistic.length > 0 ? (optimisticSuccess / optimistic.length * 100).toFixed(2) : '0.00',
        avgUserWaitTime: avgOptimisticWait.toFixed(2),
      },
      improvement: {
        waitTimeReduction: avgTraditionalWait > 0 ? ((avgTraditionalWait - avgOptimisticWait) / avgTraditionalWait * 100).toFixed(2) : '0.00',
      }
    };
  }

  reset() {
    this.credits = 100;
    this.pendingOperations = [];
    this.operationHistory = [];
  }
}

// 测试函数
async function testOptimisticUpdates() {
  console.log('🚀 开始测试前端乐观更新机制\n');
  
  const manager = new OptimisticCreditsManager(1000);
  
  // 测试1：用户体验对比
  console.log('📊 测试1: 用户体验响应时间对比');
  console.log('=' .repeat(50));
  
  // 传统方式测试
  console.log('传统方式测试（用户需要等待服务器响应）...');
  manager.reset();
  manager.credits = 1000;
  
  for (let i = 0; i < 20; i++) {
    await manager.traditionalDeduct(10, 150 + Math.random() * 100); // 150-250ms延迟
  }
  
  // 乐观更新测试
  console.log('乐观更新测试（用户立即看到结果）...');
  manager.reset();
  manager.credits = 1000;
  
  const optimisticPromises = [];
  for (let i = 0; i < 20; i++) {
    optimisticPromises.push(manager.optimisticDeduct(10, 150 + Math.random() * 100));
  }
  await Promise.all(optimisticPromises);
  
  const stats = manager.getStats();
  
  console.log('\n📈 测试结果:');
  console.log(`传统方式:`);
  console.log(`  操作次数: ${stats.traditional.count}`);
  console.log(`  成功率: ${stats.traditional.successRate}%`);
  console.log(`  平均用户等待时间: ${stats.traditional.avgUserWaitTime}ms`);
  
  console.log(`\n乐观更新:`);
  console.log(`  操作次数: ${stats.optimistic.count}`);
  console.log(`  成功率: ${stats.optimistic.successRate}%`);
  console.log(`  平均用户等待时间: ${stats.optimistic.avgUserWaitTime}ms`);
  
  console.log(`\n🎯 性能提升:`);
  console.log(`  用户等待时间减少: ${stats.improvement.waitTimeReduction}%`);
  
  // 测试2：并发操作处理
  console.log('\n📊 测试2: 并发操作处理能力');
  console.log('=' .repeat(50));
  
  manager.reset();
  manager.credits = 1000;
  
  const concurrentStart = performance.now();
  const concurrentPromises = [];
  
  // 同时发起50个操作
  for (let i = 0; i < 50; i++) {
    concurrentPromises.push(manager.optimisticDeduct(5, 100));
  }
  
  const results = await Promise.all(concurrentPromises);
  const concurrentEnd = performance.now();
  
  const successfulOps = results.filter(r => r.success).length;
  const failedOps = results.length - successfulOps;
  
  console.log(`并发操作结果:`);
  console.log(`  总操作数: ${results.length}`);
  console.log(`  成功操作: ${successfulOps}`);
  console.log(`  失败操作: ${failedOps}`);
  console.log(`  总耗时: ${(concurrentEnd - concurrentStart).toFixed(2)}ms`);
  console.log(`  平均用户等待时间: ${(results.reduce((sum, r) => sum + r.userWaitTime, 0) / results.length).toFixed(2)}ms`);
  
  // 测试3：错误恢复机制
  console.log('\n📊 测试3: 错误恢复机制');
  console.log('=' .repeat(50));
  
  manager.reset();
  manager.credits = 100;
  const initialCredits = manager.credits;
  
  // 模拟一个必定失败的操作
  const mockFailedDeduct = async (amount) => {
    const originalCredits = manager.credits;
    manager.credits -= amount; // 立即更新UI
    
    // 模拟API失败
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 回滚状态
    manager.credits = originalCredits;
    return false;
  };
  
  await mockFailedDeduct(50);
  
  console.log(`错误恢复测试:`);
  console.log(`  初始积分: ${initialCredits}`);
  console.log(`  操作失败后积分: ${manager.credits}`);
  console.log(`  ✅ 状态正确回滚: ${manager.credits === initialCredits ? '是' : '否'}`);
  
  // 验收标准检查
  console.log('\n✅ 验收标准检查:');
  console.log('=' .repeat(50));
  
  const targetResponseTime = 100; // 目标响应时间 < 100ms
  const actualResponseTime = parseFloat(stats.optimistic.avgUserWaitTime);
  
  console.log(`目标用户响应时间: < ${targetResponseTime}ms`);
  console.log(`实际用户响应时间: ${actualResponseTime}ms`);
  console.log(`✅ 响应时间达标: ${actualResponseTime < targetResponseTime ? '是' : '否'}`);
  
  const waitTimeReduction = parseFloat(stats.improvement.waitTimeReduction);
  const targetReduction = 90; // 目标减少90%等待时间
  
  console.log(`目标等待时间减少: > ${targetReduction}%`);
  console.log(`实际等待时间减少: ${waitTimeReduction}%`);
  console.log(`✅ 等待时间减少达标: ${waitTimeReduction > targetReduction ? '是' : '否'}`);
  
  console.log('\n🎉 前端乐观更新测试完成！');
}

// 运行测试
testOptimisticUpdates().catch(console.error);
