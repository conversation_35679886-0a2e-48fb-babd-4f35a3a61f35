/**
 * 积分系统缓存策略优化测试
 * 验证缓存命中率提升和功能正确性
 */

import { 
  getUserCredits, 
  increaseCredits, 
  decreaseCredits, 
  deductCreditsWithTransaction,
  refundCredits,
  updateUserCreditsCache,
  warmupCache,
  getCacheStats,
  resetCacheStats,
  clearUserCreditsCache,
  CreditsTransType 
} from '@/services/credit';

// Mock dependencies
jest.mock('@/models/credit');
jest.mock('@/models/order');
jest.mock('@/lib/time');
jest.mock('@/lib/hash');

const mockGetUserValidCredits = require('@/models/credit').getUserValidCredits;
const mockInsertCredit = require('@/models/credit').insertCredit;
const mockGetFirstPaidOrderByUserUuid = require('@/models/order').getFirstPaidOrderByUserUuid;
const mockGetIsoTimestr = require('@/lib/time').getIsoTimestr;
const mockGetSnowId = require('@/lib/hash').getSnowId;

describe('积分系统缓存策略优化', () => {
  const testUserUuid = 'test-user-123';
  const testCredits = [
    { credits: 100, order_no: 'order1', expired_at: '2025-12-31T23:59:59Z' },
    { credits: 50, order_no: 'order2', expired_at: '2025-12-31T23:59:59Z' }
  ];

  beforeEach(() => {
    // 重置缓存和统计
    clearUserCreditsCache();
    resetCacheStats();
    
    // 设置默认 mock 返回值
    mockGetUserValidCredits.mockResolvedValue(testCredits);
    mockGetFirstPaidOrderByUserUuid.mockResolvedValue({ id: 1 });
    mockInsertCredit.mockResolvedValue({});
    mockGetIsoTimestr.mockReturnValue('2024-01-01T00:00:00Z');
    mockGetSnowId.mockReturnValue('test-trans-id');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('缓存命中率测试', () => {
    test('应该在首次查询后缓存用户积分', async () => {
      const result1 = await getUserCredits(testUserUuid);
      expect(result1.left_credits).toBe(150);
      
      const result2 = await getUserCredits(testUserUuid);
      expect(result2.left_credits).toBe(150);
      
      // 第二次查询应该命中缓存，不再调用数据库
      expect(mockGetUserValidCredits).toHaveBeenCalledTimes(1);
      
      const stats = getCacheStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe('50.00%');
    });

    test('缓存过期后应该重新查询数据库', async () => {
      // 首次查询
      await getUserCredits(testUserUuid);
      
      // 模拟缓存过期（通过清除缓存）
      clearUserCreditsCache(testUserUuid);
      
      // 再次查询
      await getUserCredits(testUserUuid);
      
      // 应该调用数据库两次
      expect(mockGetUserValidCredits).toHaveBeenCalledTimes(2);
    });
  });

  describe('缓存更新策略测试', () => {
    test('增加积分后应该更新缓存而不是清除', async () => {
      // 首次查询建立缓存
      const initialCredits = await getUserCredits(testUserUuid);
      expect(initialCredits.left_credits).toBe(150);
      
      // 增加积分
      await increaseCredits({
        user_uuid: testUserUuid,
        trans_type: CreditsTransType.AdminGift,
        credits: 50
      });
      
      // 再次查询应该命中缓存（因为缓存被更新了）
      const updatedCredits = await getUserCredits(testUserUuid);
      
      const stats = getCacheStats();
      expect(stats.updates).toBe(1); // 应该有一次缓存更新
    });

    test('扣除积分后应该更新缓存', async () => {
      // 首次查询建立缓存
      await getUserCredits(testUserUuid);
      
      // 扣除积分
      await decreaseCredits({
        user_uuid: testUserUuid,
        trans_type: CreditsTransType.ImageGeneration,
        credits: 30
      });
      
      const stats = getCacheStats();
      expect(stats.updates).toBe(1); // 应该有一次缓存更新
    });

    test('事务性扣除积分后应该更新缓存', async () => {
      // 首次查询建立缓存
      await getUserCredits(testUserUuid);
      
      // 事务性扣除积分
      const result = await deductCreditsWithTransaction({
        user_uuid: testUserUuid,
        trans_type: CreditsTransType.ImageGeneration,
        credits: 30
      });
      
      expect(result.success).toBe(true);
      expect(result.remainingCredits).toBe(120);
      
      const stats = getCacheStats();
      expect(stats.updates).toBe(1); // 应该有一次缓存更新
    });

    test('退款后应该更新缓存', async () => {
      // 首次查询建立缓存
      await getUserCredits(testUserUuid);
      
      // 退款
      await refundCredits({
        user_uuid: testUserUuid,
        credits: 25,
        originalTransactionId: 'original-trans-id'
      });
      
      const stats = getCacheStats();
      expect(stats.updates).toBe(1); // 应该有一次缓存更新
    });
  });

  describe('批量操作缓存预热测试', () => {
    test('应该能够预热多个用户的缓存', async () => {
      const userUuids = ['user1', 'user2', 'user3'];
      
      // 预热缓存
      await warmupCache(userUuids);
      
      const stats = getCacheStats();
      expect(stats.cacheSize).toBeGreaterThan(0);
    });

    test('已缓存的用户不应该重复预热', async () => {
      const userUuids = [testUserUuid];
      
      // 首先查询一次建立缓存
      await getUserCredits(testUserUuid);
      
      // 预热缓存（应该跳过已缓存的用户）
      await warmupCache(userUuids);
      
      // 数据库查询次数不应该增加
      expect(mockGetUserValidCredits).toHaveBeenCalledTimes(1);
    });
  });

  describe('缓存统计功能测试', () => {
    test('应该正确统计缓存命中率', async () => {
      // 执行一些查询操作
      await getUserCredits(testUserUuid); // miss
      await getUserCredits(testUserUuid); // hit
      await getUserCredits(testUserUuid); // hit
      
      const stats = getCacheStats();
      expect(stats.hits).toBe(2);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe('66.67%');
    });

    test('应该能够重置统计信息', async () => {
      await getUserCredits(testUserUuid);
      
      let stats = getCacheStats();
      expect(stats.misses).toBe(1);
      
      resetCacheStats();
      
      stats = getCacheStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);
      expect(stats.updates).toBe(0);
    });
  });

  describe('错误处理测试', () => {
    test('缓存更新失败时应该回退到清除缓存', async () => {
      // 模拟数据库查询失败
      mockGetUserValidCredits.mockRejectedValueOnce(new Error('Database error'));
      
      // 尝试增加积分（会触发缓存更新失败）
      await increaseCredits({
        user_uuid: testUserUuid,
        trans_type: CreditsTransType.AdminGift,
        credits: 50
      });
      
      // 应该不会抛出错误，而是优雅地处理
      expect(mockInsertCredit).toHaveBeenCalled();
    });
  });
});
