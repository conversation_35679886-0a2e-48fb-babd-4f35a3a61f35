"use client";

import { useState, useRef, useEffect } from "react";
import { ImageDataSSR, ImageLoadState } from "@/types/blocks/gallery";
import { cn } from "@/lib/utils";

interface ImageCardSSRProps {
  image: ImageDataSSR;
  loadState?: ImageLoadState;
  onClick: () => void;
  onLoadStateChange: (id: string, updates: Partial<ImageLoadState>) => void;
  className?: string;
}

export default function ImageCardSSR({ 
  image, 
  loadState, 
  onClick, 
  onLoadStateChange, 
  className 
}: ImageCardSSRProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [hasImageError, setHasImageError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  // 懒加载实现 - 检测卡片是否进入视口
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // 处理图片加载成功
  const handleImageLoad = () => {
    setIsImageLoaded(true);
    onLoadStateChange(image.id, { isLoaded: true, hasError: false });
  };

  // 处理图片加载失败
  const handleImageError = () => {
    setHasImageError(true);
    onLoadStateChange(image.id, { isLoaded: false, hasError: true });
  };

  // 判断是否应该显示图片
  const shouldShowImage = isVisible && loadState?.src;

  return (
    <div
      ref={cardRef}
      className={cn(
        "group relative overflow-hidden rounded-lg bg-muted/50 cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02]",
        className
      )}
      onClick={onClick}
      style={{
        aspectRatio: image.aspectRatio,
        minHeight: "200px",
      }}
    >
      {/* 占位符 - 始终显示直到图片加载完成 */}
      {(!shouldShowImage || !isImageLoaded) && (
        <div className="absolute inset-0 bg-gradient-to-br from-muted to-muted/50 animate-pulse" />
      )}

      {/* 图片 - 只有在有src且可见时才渲染 */}
      {shouldShowImage && !hasImageError && (
        <img
          ref={imgRef}
          src={loadState.src}
          alt={image.alt}
          className={cn(
            "w-full h-full object-cover transition-all duration-500",
            isImageLoaded ? "opacity-100" : "opacity-0"
          )}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
        />
      )}

      {/* 错误状态 */}
      {hasImageError && (
        <div className="absolute inset-0 bg-muted/50 flex items-center justify-center">
          <div className="w-12 h-12 text-muted-foreground opacity-50">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
            </svg>
          </div>
        </div>
      )}

      {/* 悬停遮罩 - 只有在图片加载完成时才显示 */}
      {isImageLoaded && (
        <>
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300" />
          
          {/* 悬停时显示的放大图标 */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
            <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center backdrop-blur-sm">
              <svg className="w-6 h-6 text-brand-dark" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
              </svg>
            </div>
          </div>
        </>
      )}

      {/* 隐藏的alt文本用于SEO - 在服务端渲染 */}
      <span className="sr-only">{image.alt}</span>
    </div>
  );
}
