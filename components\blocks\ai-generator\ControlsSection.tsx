"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { AIGeneratorTranslations, StyleOption, ColorOption, LightingOption, CompositionOption } from "@/types/blocks/ai-generator";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Square,
  Palette,
  Sparkles,
  Sun,
  Grid,
  X,
  Camera,
  Zap,
  Flame,
  Heart,
  Moon,
  Crown,
  Smile,
  Pencil,
  Circle,
  Contrast,
  Star,
  Rainbow,
  Layers,
  Shapes,
  Wind,
  Book,
  Clock,
  Flower,
  Skull,
  Snowflake,
  Mountain,
  Sunrise,
  ArrowUp,
  ArrowDown,
  Globe,
  Flashlight,
  Sunset,
  Grid3X3,
  Target,
  FlipHorizontal,
  ArrowRight,
  Frame,
  Eye,
  User,
  ZoomIn,
  Slash,
  RotateCcw,
  FlipVertical,
  Ban,
  Coins
} from "lucide-react";
import { calculateImageGenerationCredits } from "@/services/credit";
import { GenerationSettings } from "@/types/credit";
import { useTranslations } from "next-intl";

interface ControlsSectionProps {
  mode: "text-to-image" | "image-to-image";
  activeTab?: string;
  settings: {
    ratio: string;
    style: string;
    color: string;
    lighting: string;
    composition: string;
    imageCount: string;
  };
  setSettings: React.Dispatch<React.SetStateAction<{
    ratio: string;
    style: string;
    color: string;
    lighting: string;
    composition: string;
    imageCount: string;
  }>>;
  settingsOptions: {
    ratio: string[];
    style: StyleOption[];
    color: ColorOption[];
    lighting: LightingOption[];
    composition: CompositionOption[];
    imageCount: any[];
  };
  enableHighQuality: boolean;
  setEnableHighQuality: (value: boolean) => void;
  textPrompt: string;
  setTextPrompt: (value: string) => void;
  imageEnableHighQuality: boolean;
  setImageEnableHighQuality: (value: boolean) => void;
  uploadedImage: string | null;
  setUploadedImage: (value: string | null) => void;
  imagePrompt: string;
  setImagePrompt: (value: string) => void;
  isGenerating: boolean;
  handleGenerate: () => void;
  translations?: AIGeneratorTranslations;
  creditCheckResult?: {
    sufficient: boolean;
    currentCredits: number;
    requiredCredits: number;
  } | null;
  isCheckingCredits?: boolean;
}

export default function ControlsSection({
  mode,
  activeTab,
  settings,
  setSettings,
  settingsOptions,
  enableHighQuality,
  setEnableHighQuality,
  textPrompt,
  setTextPrompt,
  imageEnableHighQuality,
  setImageEnableHighQuality,
  uploadedImage,
  setUploadedImage,
  imagePrompt,
  setImagePrompt,
  isGenerating,
  handleGenerate,
  translations,
  creditCheckResult,
  isCheckingCredits
}: ControlsSectionProps) {
  const isMobile = useIsMobile();
  const [expandedSetting, setExpandedSetting] = useState<string | null>(null);
  const [panelHeight, setPanelHeight] = useState<number>(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const tCredits = useTranslations("credits.generation");

  // 监听页签变化，清除展开状态
  useEffect(() => {
    if (activeTab) {
      setExpandedSetting(null);
    }
  }, [activeTab]);

  // 动态计算面板高度
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (expandedSetting && panelRef.current) {
      if (isMobile) {
        // 移动端：直接设置高度，无动画
        const height = panelRef.current.scrollHeight;
        setPanelHeight(height);
        setIsAnimating(false);
      } else {
        // 桌面端：保持原有动画
        setIsAnimating(true);
        // 使用双重 requestAnimationFrame 确保 DOM 完全更新后再测量
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            if (panelRef.current) {
              const height = panelRef.current.scrollHeight;
              setPanelHeight(height);

              // 动画完成后重置动画状态
              timer = setTimeout(() => setIsAnimating(false), 300);
            }
          });
        });
      }
    } else {
      if (isMobile) {
        // 移动端：直接设置高度，无动画
        setPanelHeight(0);
        setIsAnimating(false);
      } else {
        // 桌面端：保持原有动画
        setIsAnimating(true);
        setPanelHeight(0);

        // 动画完成后重置动画状态
        timer = setTimeout(() => setIsAnimating(false), 300);
      }
    }

    // 清理函数
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [expandedSetting, settingsOptions, isMobile]);

  // 当展开的选项内容发生变化时，重新计算高度
  useEffect(() => {
    if (expandedSetting && panelRef.current) {
      if (isMobile) {
        // 移动端：直接设置高度，无延迟
        const height = panelRef.current.scrollHeight;
        setPanelHeight(height);
      } else {
        // 桌面端：添加小延迟确保内容完全渲染
        const timer = setTimeout(() => {
          if (panelRef.current) {
            const height = panelRef.current.scrollHeight;
            setPanelHeight(height);
          }
        }, 50);

        return () => clearTimeout(timer);
      }
    }
  }, [expandedSetting, settings, settingsOptions, isMobile]);

  const handleSettingClick = (setting: string) => {
    if (expandedSetting === setting) {
      // 情况1：点击当前展开的选项 → 收起
      setExpandedSetting(null);
    } else if (expandedSetting !== null) {
      // 情况2：点击不同的选项 → 先收起再展开
      if (isMobile) {
        // 移动端：直接切换，无动画延迟
        setExpandedSetting(setting);
      } else {
        // 桌面端：先收起再展开
        setExpandedSetting(null);
        setTimeout(() => {
          setExpandedSetting(setting);
        }, 300); // 等待收起动画完成
      }
    } else {
      // 情况3：当前没有展开的选项 → 直接展开
      setExpandedSetting(setting);

      // 移动端：展开设置时平滑滚动到设置下拉框，考虑header遮挡
      if (isMobile && panelRef.current) {
        setTimeout(() => {
          const panel = panelRef.current;
          if (panel) {
            const rect = panel.getBoundingClientRect();
            const headerHeight = 80; // 假设header高度为80px，可以根据实际情况调整
            const targetY = window.scrollY + rect.top - headerHeight;

            window.scrollTo({
              top: targetY,
              behavior: 'smooth'
            });
          }
        }, 100); // 小延迟确保面板已渲染
      }
    }
  };

  // 图标映射函数
  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: any } = {
      X, Camera, Square, Zap, Flame, Heart, Moon, Crown, Smile, Pencil,
      Circle, Sparkles, Contrast, Star, Rainbow, Layers, Sun, Shapes,
      Wind, Book, Clock, Palette, Flower, Skull, Snowflake, Mountain,
      Sunrise, ArrowUp, ArrowDown, Globe, Flashlight, Sunset, Grid3X3,
      Target, FlipHorizontal, ArrowRight, Frame, Eye, User, ZoomIn,
      Slash, RotateCcw, FlipVertical, Ban
    };
    return iconMap[iconName] || Circle;
  };

  const renderDropdowns = () => {
    // 获取当前语言环境（简单判断，可以根据实际需求调整）
    const currentLocale = translations?.settings?.style === 'Style' ? 'en' : 'zh';

    // 获取风格显示文本的辅助函数
    const getStyleDisplayText = (styleOption: StyleOption) => {
      return currentLocale === 'en' ? styleOption.en : styleOption.zh;
    };

    // 获取当前选中选项的图标
    const getCurrentOptionIcon = (settingKey: string, currentValue: string) => {
      const settingOptions = {
        style: settingsOptions.style,
        color: settingsOptions.color,
        lighting: settingsOptions.lighting,
        composition: settingsOptions.composition,
        imageCount: settingsOptions.imageCount
      };

      const options = settingOptions[settingKey as keyof typeof settingOptions];
      if (Array.isArray(options) && options.length > 0 && typeof options[0] === 'object') {
        // 获取当前语言环境
        const currentLocale = translations?.settings?.style === 'Style' ? 'en' : 'zh';

        const currentOption = options.find((option: any) => {
          const displayText = currentLocale === 'en' ? option.en : option.zh;
          return displayText === currentValue;
        });
        if (currentOption && currentOption.icon) {
          return getIconComponent(currentOption.icon);
        }
      }

      // 回退到默认图标
      const defaultIcons = {
        style: Sparkles,
        color: Palette,
        lighting: Sun,
        composition: Grid,
        imageCount: Grid3X3
      };
      return defaultIcons[settingKey as keyof typeof defaultIcons] || Square;
    };

    // 设置按钮配置数组，按重要性排序
    const settingButtons = [
      {
        key: 'ratio',
        icon: Square,
        value: settings.ratio,
        label: translations?.settings?.ratio || '比例'
      },
      {
        key: 'style',
        icon: getCurrentOptionIcon('style', settings.style),
        value: settings.style,
        label: translations?.settings?.style || '风格'
      },
      {
        key: 'color',
        icon: getCurrentOptionIcon('color', settings.color),
        value: settings.color,
        label: translations?.settings?.color || '色彩'
      },
      {
        key: 'lighting',
        icon: getCurrentOptionIcon('lighting', settings.lighting),
        value: settings.lighting,
        label: translations?.settings?.lighting || '光影'
      },
      {
        key: 'composition',
        icon: getCurrentOptionIcon('composition', settings.composition),
        value: settings.composition,
        label: translations?.settings?.composition || '构图'
      },
      {
        key: 'imageCount',
        icon: getCurrentOptionIcon('imageCount', settings.imageCount),
        value: `${settings.imageCount}${translations?.settings?.imageCount_unit ?? '张'}`,
        label: translations?.settings?.imageCount || '数量'
      }
    ];

    // 动态计算按钮宽度的函数
    const getButtonWidth = (text: string) => {
      // 基础宽度：图标(16px) + 间距(4px) + 内边距(32px) = 52px
      // 文本宽度：中文字符约10px，英文字符约6px（text-sm字体）
      const baseWidth = 52;
      // 检测是否包含中文字符
      const hasChinese = /[\u4e00-\u9fff]/.test(text);
      const charWidth = hasChinese ? 10 : 6;
      const textWidth = text.length * charWidth;
      const totalWidth = baseWidth + textWidth;

      // 设置最小和最大宽度限制，为中文提供更多空间
      return Math.max(90, Math.min(170, totalWidth));
    };

    return (
      <div className="flex flex-wrap gap-1.5">
        {settingButtons.map(({ key, icon: IconComponent, value, label }) => (
          <Button
            key={key}
            variant="ghost"
            size="sm"
            style={{ width: `${getButtonWidth(value)}px` }}
            className={`
              h-9 text-white hover:text-white hover:bg-white/10
              justify-center transition-all duration-200 ease-in-out
              border border-transparent hover:border-white/20 px-4 flex-shrink-0
              ${expandedSetting === key ? 'bg-white/20 border-white/30' : ''}
            `}
            onClick={() => handleSettingClick(key)}
            title={`${label}: ${value}`}
          >
            <div className="flex items-center gap-2 min-w-0 w-full justify-center">
              <IconComponent className="h-4 w-4 flex-shrink-0" />
              <span className="text-sm font-medium whitespace-nowrap">
                {value}
              </span>
            </div>
          </Button>
        ))}
      </div>
    );
  };

  const renderExpandedPanel = () => {
    // 始终渲染面板，但通过样式控制显示/隐藏
    const shouldShow = expandedSetting !== null;

    // 获取当前语言环境
    const currentLocale = translations?.settings?.style === 'Style' ? 'en' : 'zh';

    // 定义imageCount选项类型
    type ImageCountOption = {
      zh: string;
      en: string;
      icon: string;
    };

    // 获取选项显示文本的辅助函数（通用）
    const getOptionDisplayText = (option: StyleOption | ColorOption | LightingOption | CompositionOption | ImageCountOption) => {
      return currentLocale === 'en' ? option.en : option.zh;
    };

    // 获取选项的唯一键（通用）
    const getOptionKey = (option: StyleOption | ColorOption | LightingOption | CompositionOption | ImageCountOption) => {
      return `${option.zh}-${option.en}`;
    };

    const getSettingConfig = (setting: string | null) => {
      if (!setting) return null;

      switch (setting) {
        case 'ratio':
          return {
            icon: Square,
            label: translations?.settings?.ratio || '比例',
            options: settingsOptions.ratio,
            currentValue: settings.ratio,
            updateSetting: (value: string) => setSettings(prev => ({ ...prev, ratio: value })),
            isStyleSetting: false
          };
        case 'style':
          return {
            icon: Sparkles,
            label: translations?.settings?.style || '风格',
            options: settingsOptions.style,
            currentValue: settings.style,
            updateSetting: (value: string) => setSettings(prev => ({ ...prev, style: value })),
            isSpecialSetting: true,
            getOptionDisplayText,
            getOptionKey
          };
        case 'color':
          return {
            icon: Palette,
            label: translations?.settings?.color || '色彩',
            options: settingsOptions.color,
            currentValue: settings.color,
            updateSetting: (value: string) => setSettings(prev => ({ ...prev, color: value })),
            isSpecialSetting: true,
            getOptionDisplayText,
            getOptionKey
          };
        case 'lighting':
          return {
            icon: Sun,
            label: translations?.settings?.lighting || '光影',
            options: settingsOptions.lighting,
            currentValue: settings.lighting,
            updateSetting: (value: string) => setSettings(prev => ({ ...prev, lighting: value })),
            isSpecialSetting: true,
            getOptionDisplayText,
            getOptionKey
          };
        case 'composition':
          return {
            icon: Grid,
            label: translations?.settings?.composition || '构图',
            options: settingsOptions.composition,
            currentValue: settings.composition,
            updateSetting: (value: string) => setSettings(prev => ({ ...prev, composition: value })),
            isSpecialSetting: true,
            getOptionDisplayText,
            getOptionKey
          };
        case 'imageCount':
          return {
            icon: Grid3X3,
            label: translations?.settings?.imageCount || '数量',
            options: settingsOptions.imageCount,
            currentValue: settings.imageCount,
            updateSetting: (value: string) => setSettings(prev => ({ ...prev, imageCount: value })),
            isSpecialSetting: true,
            getOptionDisplayText,
            getOptionKey
          };
        default:
          return null;
      }
    };

    const config = getSettingConfig(expandedSetting);
    const IconComponent = config?.icon;

    return (
      <div
        ref={panelRef}
        className={`overflow-hidden ${isMobile ? '' : 'transition-all duration-300 ease-out'}`}
        style={{
          height: panelHeight,
          opacity: shouldShow ? 1 : 0,
          transform: isMobile ? 'none' : (shouldShow ? 'translateY(0)' : 'translateY(-8px)')
        }}
      >
        <div className="m-0 bg-transparent rounded-lg">
          <div className="p-6">
            <div className="space-y-3">
              {config && (
                <>
                  <div className="flex items-center gap-2 text-white text-sm font-medium">
                    {IconComponent && <IconComponent className="h-4 w-4" />}
                    <span>{config.label}</span>
                  </div>
                  <div className={`grid gap-2 ${
                    expandedSetting === 'imageCount'
                      ? 'grid-cols-4'
                      : config.isSpecialSetting
                        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                        : 'grid-cols-2 md:grid-cols-4 lg:grid-cols-6'
                  }`}>
                    {config.options.map((option: any) => {
                      const isSpecialOption = config.isSpecialSetting;
                      let displayText: string;
                      let optionKey: string;
                      let isSelected: boolean;

                      if (isSpecialOption && config.getOptionDisplayText && config.getOptionKey) {
                        displayText = config.getOptionDisplayText(option);
                        optionKey = config.getOptionKey(option);
                        isSelected = config.currentValue === displayText;
                      } else {
                        displayText = option;
                        optionKey = option;
                        isSelected = config.currentValue === option;
                      }

                      // 获取选项特定的图标
                      const OptionIcon = isSpecialOption && option.icon
                        ? getIconComponent(option.icon)
                        : IconComponent;

                      return (
                        <Button
                          key={optionKey}
                          variant="ghost"
                          size="sm"
                          className={`text-white hover:bg-white/10 justify-start text-left ${isSpecialOption ? 'h-auto py-3 px-4' : 'py-2 px-4'} ${
                            isSelected ? 'bg-white/20' : ''
                          }`}
                          onClick={() => {
                            if (isSpecialOption) {
                              config.updateSetting(displayText);
                            } else {
                              config.updateSetting(option);
                            }

                            // 移动端：选择选项后先滚动到文本输入框中央，再收起下拉框
                            if (isMobile) {
                              const currentExpandedSetting = expandedSetting; // 保存当前展开的设置
                              // 先滚动到文本输入框中央
                              setTimeout(() => {
                                // 查找当前可见的文本输入框
                                const textInputs = document.querySelectorAll('[data-text-input]');
                                let visibleTextInput = null;

                                // 找到当前可见的文本输入框
                                textInputs.forEach(input => {
                                  if (input.offsetParent !== null) { // 检查元素是否可见
                                    visibleTextInput = input;
                                  }
                                });

                                if (visibleTextInput) {
                                  visibleTextInput.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                  });
                                }

                                // 滚动完成后再收起下拉框
                                setTimeout(() => {
                                  setExpandedSetting(null); // 直接收起，不调用handleSettingClick避免逻辑混乱
                                }, 500); // 等待滚动动画完成
                              }, 100); // 小延迟确保设置已更新
                            } else {
                              // 桌面端：直接收起下拉框
                              handleSettingClick(expandedSetting!);
                            }
                          }}
                        >
                          <div className="flex items-center gap-2 w-full min-w-0">
                            {OptionIcon && <OptionIcon className="h-3 w-3 flex-shrink-0" />}
                            {isSpecialOption ? (
                              <div className="flex items-center justify-between w-full min-w-0">
                                <span className="text-left truncate">{displayText}</span>
                                {/* 为图片数量选择器显示积分标识 */}
                                {expandedSetting === 'imageCount' ? (
                                  <div className="flex items-center gap-1 text-orange-400 text-xs ml-2 flex-shrink-0">
                                    <Coins className="h-3 w-3" />
                                    <span>{tCredits("multiplier_label", { count: displayText })}</span>
                                  </div>
                                ) : option.reference && (
                                  <span className="text-xs text-white/60 ml-2 truncate flex-shrink-0">
                                    {option.reference}
                                  </span>
                                )}
                              </div>
                            ) : (
                              <span className="text-left truncate">{displayText}</span>
                            )}
                          </div>
                        </Button>
                      );
                    })}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (mode === "text-to-image") {
    return (
      <div className="controls-section border-t border-white/20 bg-black/10 backdrop-blur-sm">
        <div className="p-4 md:p-6">
          {/* 响应式布局：小屏幕垂直，大屏幕水平 */}
          <div className="flex flex-col lg:flex-row lg:items-center gap-3 lg:gap-4">
            {/* 设置面板 */}
            <div className="flex-1 min-w-0 overflow-hidden">
              {renderDropdowns()}
            </div>

            {/* 控制区域 - 高质量开关 + 生成按钮 */}
            <div className="flex items-center justify-between lg:justify-end gap-3 lg:gap-4 flex-shrink-0 lg:w-auto">
              {/* 高质量开关 */}
              <div className="flex items-center gap-2 whitespace-nowrap">
                <Switch
                  checked={enableHighQuality}
                  onCheckedChange={setEnableHighQuality}
                  className="data-[state=checked]:bg-white/20"
                />
                <span className="text-white text-sm">
                  {translations?.controls?.high_quality || "高质量"}
                </span>
              </div>

              {/* 积分显示和生成按钮 */}
              <div className="flex flex-col items-end gap-2">
                {/* 积分计算显示 */}
                {(() => {
                  const generationSettings: GenerationSettings = {
                    imageCount: parseInt(settings.imageCount) || 1,
                    highQuality: enableHighQuality
                  };
                  const calculation = calculateImageGenerationCredits(generationSettings);
                  const isInsufficientCredits = creditCheckResult && !creditCheckResult.sufficient;

                  return (
                    <div className="flex flex-col items-end gap-1">
                      <div className={`flex items-center gap-1 text-xs ${isInsufficientCredits ? 'text-red-400' : 'text-orange-400'}`}>
                        <Coins className="h-3 w-3" />
                        <span>{tCredits("required_credits", { credits: calculation.totalCredits })}</span>
                      </div>
                      {isInsufficientCredits && (
                        <div className="text-xs text-red-400">
                          {tCredits("insufficient", { current: creditCheckResult.currentCredits })}
                        </div>
                      )}
                    </div>
                  );
                })()}

                {/* 生成按钮 */}
                <Button
                  onClick={handleGenerate}
                  disabled={!textPrompt.trim() || isGenerating || isCheckingCredits}
                  size="default"
                  className="min-w-[110px] px-3 py-2 flex-shrink-0"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">{translations?.controls?.generating || "生成中..."}</span>
                    </>
                  ) : isCheckingCredits ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">{tCredits("checking")}</span>
                    </>
                  ) : (
                    <span className="text-sm">{translations?.controls?.generate || "生成图片"}</span>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
        {renderExpandedPanel()}
      </div>
    );
  }

  return (
    <div className="controls-section border-t border-white/20 bg-black/10 backdrop-blur-sm">
      <div className="p-4 md:p-6">
        {/* 响应式布局：小屏幕垂直，大屏幕水平 */}
        <div className="flex flex-col lg:flex-row lg:items-center gap-3 lg:gap-4">
          {/* 设置面板 */}
          <div className="flex-1 min-w-0 overflow-hidden">
            {renderDropdowns()}
          </div>

          {/* 控制区域 - 高质量开关 + 生成按钮 */}
          <div className="flex items-center justify-between lg:justify-end gap-3 lg:gap-4 flex-shrink-0 lg:w-auto">
            {/* 高质量开关 */}
            <div className="flex items-center gap-2 whitespace-nowrap">
              <Switch
                checked={imageEnableHighQuality}
                onCheckedChange={setImageEnableHighQuality}
                className="data-[state=checked]:bg-white/20"
              />
              <span className="text-white text-sm">
                {translations?.controls?.high_quality || "高质量"}
              </span>
            </div>

            {/* 积分显示和生成按钮 */}
            <div className="flex flex-col items-end gap-2">
              {/* 积分计算显示 */}
              {(() => {
                const generationSettings: GenerationSettings = {
                  imageCount: parseInt(settings.imageCount) || 1,
                  highQuality: mode === "text-to-image" ? enableHighQuality : imageEnableHighQuality
                };
                const calculation = calculateImageGenerationCredits(generationSettings);
                const isInsufficientCredits = creditCheckResult && !creditCheckResult.sufficient;

                return (
                  <div className="flex flex-col items-end gap-1">
                    <div className={`flex items-center gap-1 text-xs ${isInsufficientCredits ? 'text-red-400' : 'text-orange-400'}`}>
                      <Coins className="h-3 w-3" />
                      <span>{tCredits("required_credits", { credits: calculation.totalCredits })}</span>
                    </div>
                    {isInsufficientCredits && (
                      <div className="text-xs text-red-400">
                        {tCredits("insufficient", { current: creditCheckResult.currentCredits })}
                      </div>
                    )}
                  </div>
                );
              })()}

              {/* 生成按钮 */}
              <Button
                onClick={handleGenerate}
                disabled={
                  mode === "text-to-image"
                    ? !textPrompt.trim() || isGenerating || isCheckingCredits
                    : !uploadedImage || !imagePrompt.trim() || isGenerating || isCheckingCredits
                }
                size="default"
                className="min-w-[110px] px-3 py-2 flex-shrink-0"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    <span className="text-sm">{translations?.controls?.generating || "生成中..."}</span>
                  </>
                ) : isCheckingCredits ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    <span className="text-sm">{tCredits("checking")}</span>
                  </>
                ) : (
                  <span className="text-sm">{translations?.controls?.generate || "生成图片"}</span>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
      {renderExpandedPanel()}
    </div>
  );
}