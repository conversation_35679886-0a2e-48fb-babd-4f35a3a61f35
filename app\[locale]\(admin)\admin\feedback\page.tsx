import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getFeedbacks, getFeedbackStats } from "@/models/feedback";
import moment from "moment";
import { Badge } from "@/components/ui/badge";
import { Star, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import FeedbackFilters from "./components/FeedbackFilters";
import FeedbackStats from "./components/FeedbackStats";

interface SearchParams {
  status?: string;
  feedback_type?: string;
  page?: string;
  limit?: string;
}

export default async function AdminFeedbackPage({
  searchParams,
}: {
  searchParams: SearchParams;
}) {
  // 解析查询参数
  const status = searchParams.status && searchParams.status !== "all" ? searchParams.status : undefined;
  const feedback_type = searchParams.feedback_type && searchParams.feedback_type !== "all" ? searchParams.feedback_type : undefined;
  const page = parseInt(searchParams.page || "1");
  const limit = parseInt(searchParams.limit || "100");

  // 获取反馈数据和统计信息
  const feedbacks = await getFeedbacks(page, limit, status, feedback_type);
  const stats = await getFeedbackStats();

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "Pending", variant: "secondary" as const, icon: Clock },
      in_progress: { label: "In Progress", variant: "default" as const, icon: AlertCircle },
      resolved: { label: "Resolved", variant: "default" as const, icon: CheckCircle },
      closed: { label: "Closed", variant: "outline" as const, icon: XCircle },
      rejected: { label: "Rejected", variant: "destructive" as const, icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  // 获取反馈类型徽章
  const getTypeBadge = (type: string) => {
    const typeConfig = {
      bug: { label: "Bug", variant: "destructive" as const },
      feature: { label: "Feature", variant: "default" as const },
      general: { label: "General", variant: "secondary" as const },
      complaint: { label: "Complaint", variant: "destructive" as const },
      suggestion: { label: "Suggestion", variant: "default" as const },
      praise: { label: "Praise", variant: "default" as const },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.general;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  // 渲染评分
  const renderRating = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    );
  };

  const columns: TableColumn[] = [
    {
      name: "feedback_type",
      title: "Type",
      callback: (row) => getTypeBadge(row.feedback_type),
    },
    {
      name: "title",
      title: "Title",
      callback: (row) => (
        <div className="max-w-xs">
          <div className="font-medium truncate">{row.title || "No title"}</div>
          <div className="text-sm text-gray-500 truncate">{row.content}</div>
        </div>
      ),
    },
    {
      name: "user_email",
      title: "User",
      callback: (row) => row.user_email || "Anonymous",
    },
    {
      name: "rating",
      title: "Rating",
      callback: (row) => renderRating(row.rating),
    },
    {
      name: "status",
      title: "Status",
      callback: (row) => getStatusBadge(row.status),
    },
    {
      name: "created_at",
      title: "Created",
      callback: (row) => moment(row.created_at).format("MM-DD HH:mm"),
    },
  ];

  const table: TableSlotType = {
    title: "Feedback Management",
    description: `Total: ${stats?.total || 0} feedbacks | Pending: ${stats?.pending || 0} | Resolved: ${stats?.resolved || 0}`,
    columns,
    data: feedbacks,
    empty_message: "No feedback found",
  };

  return (
    <>
      {/* 统计信息 */}
      <FeedbackStats stats={stats} />

      {/* 过滤器 */}
      <FeedbackFilters
        currentStatus={searchParams.status || "all"}
        currentType={searchParams.feedback_type || "all"}
      />

      {/* 表格 */}
      <TableSlot {...table} />
    </>
  );
}
