"use client";

import React, { useState, useRef } from "react";
import { AIGenerator as AIGeneratorType } from "@/types/blocks/ai-generator";
import { GenerationSettings } from "@/types/image-results";
import AIGenerator from "@/components/blocks/ai-generator";
import ImageResultContainer from "@/components/blocks/image-results/ImageResultContainer";
import { useIsMobile } from "@/hooks/use-mobile";

// 生成参数接口（包含唯一标识符和时间戳）
interface GenerationParams {
  id: string;
  prompt: string;
  settings: GenerationSettings;
  mode: 'text-to-image' | 'image-to-image';
  uploadedImage?: string;
  timestamp: number;
}

interface AIGeneratorWithResultsProps {
  generator: AIGeneratorType;
}

export default function AIGeneratorWithResults({ generator }: AIGeneratorWithResultsProps) {
  // 使用数组存储多个生成结果，支持持久化显示
  const [generations, setGenerations] = useState<GenerationParams[]>([]);
  const isMobile = useIsMobile();

  // 用于存储AI生成器的状态重置函数
  const resetGeneratingStateRef = useRef<(() => void) | null>(null);

  // 处理新的图片生成请求
  const handleGenerate = (params: Omit<GenerationParams, 'id' | 'timestamp'>) => {
    // 创建新的生成参数，包含唯一ID和时间戳
    const newGeneration: GenerationParams = {
      ...params,
      id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    // 将新生成添加到数组开头（最新的在顶部）
    setGenerations(prev => [newGeneration, ...prev]);

    // 移动端：生成按钮点击后平滑滚动到最新的image-result中间
    if (isMobile) {
      setTimeout(() => {
        // 查找最新的image-result容器
        const imageResultContainers = document.querySelectorAll('[data-image-result-container]');
        if (imageResultContainers.length > 0) {
          const latestContainer = imageResultContainers[0]; // 最新的在顶部
          latestContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // 小延迟确保新的image-result已渲染
    }
  };

  // 处理移除特定生成容器
  const handleRemoveGeneration = (id: string) => {
    setGenerations(prev => prev.filter(gen => gen.id !== id));
  };

  // 处理生成完成，重置AI生成器的按钮状态
  const handleGenerationComplete = (generationId: string) => {
    console.log(`[AIGeneratorWithResults] 生成完成: ${generationId}`);

    // 调用AI生成器的状态重置函数
    if (resetGeneratingStateRef.current) {
      resetGeneratingStateRef.current();
      console.log('[AIGeneratorWithResults] 已重置AI生成器按钮状态');
    }
  };

  return (
    <section className="py-0 lg:py-16">
      <div className="container px-4 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* AI 生成器组件 */}
          <AIGenerator
            generator={generator}
            onGenerate={handleGenerate}
            resetGeneratingStateRef={resetGeneratingStateRef}
          />

          {/* 多个图片结果容器 - 按时间顺序显示（最新在顶部） */}
          {generations.length > 0 && (
            <div className="mt-8 space-y-8">
              {generations.map((generation) => (
                <ImageResultContainer
                  key={generation.id}
                  generationParams={generation}
                  enableCountdown={true}
                  onCancel={() => handleRemoveGeneration(generation.id)}
                  onGenerationComplete={() => handleGenerationComplete(generation.id)}
                  translations={generator.image_results}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
