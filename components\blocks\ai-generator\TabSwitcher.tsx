"use client";

import { Tabs } from "@/components/ui/tabs";
import { useIsMobile } from "@/hooks/use-mobile";
import { AIGeneratorTranslations } from "@/types/blocks/ai-generator";

interface TabSwitcherProps {
  activeTab: string;
  isAnimating: boolean;
  onTabChange: (newTab: string) => void;
  translations?: AIGeneratorTranslations;
}

export default function TabSwitcher({ activeTab, isAnimating, onTabChange, translations }: TabSwitcherProps) {
  const isMobile = useIsMobile();

  const handleTabChange = (newTab: string) => {
    if (newTab === activeTab || isAnimating) return;
    onTabChange(newTab);
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
      <div className="relative grid w-full grid-cols-2 h-14 rounded-full bg-black/20 backdrop-blur-md border border-white/20 p-1">
        {/* 滑动指示器 */}
        <div
          className={`absolute top-1 bottom-1 w-1/2 bg-white/10 backdrop-blur-md rounded-full shadow-sm will-change-transform ${
            isMobile ? '' : 'transition-transform duration-300 ease-in-out'
          }`}
          style={{
            transform: activeTab === "text-to-image" ? "translateX(0)" : "translateX(100%)",
            backfaceVisibility: "hidden"
          }}
        />

        {/* 页签按钮 */}
        <button
          onClick={() => handleTabChange("text-to-image")}
          className={`relative z-10 inline-flex items-center justify-center whitespace-nowrap px-4 py-2 text-lg font-semibold transition-colors duration-300 ease-in-out rounded-full ${
            activeTab === "text-to-image"
              ? "text-white"
              : "text-white/70 hover:text-white"
          }`}
        >
          {translations?.tabs?.text_to_image || "Text to Image"}
        </button>

        <button
          onClick={() => handleTabChange("image-to-image")}
          className={`relative z-10 inline-flex items-center justify-center whitespace-nowrap px-4 py-2 text-lg font-semibold transition-colors duration-300 ease-in-out rounded-full ${
            activeTab === "image-to-image"
              ? "text-white"
              : "text-white/70 hover:text-white"
          }`}
        >
          {translations?.tabs?.image_to_image || "Image to Image"}
        </button>
      </div>
    </Tabs>
  );
}
