"use client";

import { Coins, AlertCircle, RefreshCw, Clock } from "lucide-react";
import { useCredits } from "@/contexts/credits";
import { useTranslations } from "next-intl";

export default function CreditsDisplay() {
  const {
    credits,
    loading,
    error,
    pendingOperations,
    refreshCredits,
    clearError
  } = useCredits();
  const t = useTranslations("credits.display");
  const tErrors = useTranslations("credits.errors");

  // 如果没有积分数据且不在加载中，不显示组件
  if (!credits && !loading) {
    return null;
  }

  // 错误状态显示
  if (error && !loading) {
    return (
      <div
        className="flex items-center gap-2 px-3 py-1 bg-red-500/10 rounded-full border border-red-500/20 cursor-pointer hover:bg-red-500/20 transition-colors"
        onClick={() => {
          clearError();
          refreshCredits();
        }}
        title="点击重试获取积分信息"
      >
        <AlertCircle className="w-4 h-4 text-red-500" />
        <span className="text-sm font-medium text-red-400">
          {tErrors("fetch_failed") || "获取失败"}
        </span>
        <RefreshCw className="w-3 h-3 text-red-400" />
      </div>
    );
  }

  // 显示积分，包含待处理操作的视觉提示
  const hasPendingOperations = pendingOperations.length > 0;

  return (
    <div className={`flex items-center gap-2 px-3 py-1 rounded-full border transition-all duration-200 ${
      hasPendingOperations
        ? 'bg-yellow-500/10 border-yellow-500/20'
        : 'bg-orange-500/10 border-orange-500/20'
    }`}>
      <Coins className={`w-4 h-4 ${
        hasPendingOperations ? 'text-yellow-500' : 'text-orange-500'
      }`} />
      <span className="text-sm font-medium text-white">
        {loading ? t("loading") || "加载中..." : credits?.left_credits || 0}
      </span>
      {hasPendingOperations && (
        <Clock className="w-3 h-3 text-yellow-400 animate-pulse" title="积分操作处理中..." />
      )}
    </div>
  );
}
