import { GallerySection, GallerySectionSSR, ImageDataSSR } from "@/types/blocks/gallery";
import SSRGallery from "./SSRGallery";
import { cn } from "@/lib/utils";

interface GalleryProps {
  gallery: GallerySection;
  className?: string;
}

// 转换函数：将完整的ImageData转换为SSR版本
function convertToSSRImageData(images: any[]): ImageDataSSR[] {
  return images.map(image => ({
    id: image.id,
    alt: image.alt,
    width: image.width,
    height: image.height,
    aspectRatio: image.aspectRatio,
    style: image.style,
    color: image.color,
    lighting: image.lighting,
    composition: image.composition,
    prompt: image.prompt,
    tags: image.tags
  }));
}

export default function Gallery({ gallery, className }: GalleryProps) {
  // 转换gallery数据为SSR格式
  const ssrGallery: GallerySectionSSR = {
    title: gallery.title,
    subtitle: gallery.subtitle,
    images: gallery.images ? convertToSSRImageData(gallery.images) : undefined,
    translations: gallery.translations
  };

  // 统一使用SSR + 客户端延迟加载的混合模式
  return <SSRGallery gallery={ssrGallery} className={className} />;
}
