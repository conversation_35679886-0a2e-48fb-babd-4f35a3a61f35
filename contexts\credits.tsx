"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useAppContext } from '@/contexts/app';
import { toast } from 'sonner';

interface UserCredits {
  left_credits: number;
  is_pro?: boolean;
  is_recharged?: boolean;
}

interface PendingOperation {
  id: string;
  type: 'deduct' | 'add' | 'refund';
  amount: number;
  timestamp: number;
  promise: Promise<any>;
}

interface CreditsContextType {
  credits: UserCredits | null;
  loading: boolean;
  error: boolean;
  pendingOperations: PendingOperation[];
  
  // 核心方法
  fetchCredits: () => Promise<void>;
  optimisticDeduct: (amount: number, transType: string, description?: string) => Promise<boolean>;
  optimisticAdd: (amount: number, reason?: string) => Promise<boolean>;
  optimisticRefund: (amount: number, originalTransactionId: string, reason?: string) => Promise<boolean>;
  
  // 工具方法
  refreshCredits: () => void;
  clearError: () => void;
}

const CreditsContext = createContext<CreditsContextType | undefined>(undefined);

export function CreditsProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAppContext();
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const [pendingOperations, setPendingOperations] = useState<PendingOperation[]>([]);

  // 获取积分信息
  const fetchCredits = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(false);

      const response = await fetch('/api/user/credits');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.code === 0 && result.data?.credits) {
        setCredits(result.data.credits);
      } else {
        throw new Error(result.message || 'Failed to fetch credits');
      }
    } catch (error) {
      console.error('Failed to fetch credits:', error);
      setError(true);
      toast.error("获取积分信息失败，请重试");
    } finally {
      setLoading(false);
    }
  }, [user]);

  // 乐观扣除积分
  const optimisticDeduct = useCallback(async (
    amount: number, 
    transType: string, 
    description?: string
  ): Promise<boolean> => {
    if (!credits || credits.left_credits < amount) {
      toast.error("积分不足，无法完成操作");
      return false;
    }

    const operationId = `deduct_${Date.now()}_${Math.random()}`;
    
    // 立即更新UI
    const originalCredits = credits;
    setCredits(prev => prev ? {
      ...prev,
      left_credits: prev.left_credits - amount,
      is_pro: (prev.left_credits - amount) > 0
    } : null);

    // 创建后台操作
    const operation: PendingOperation = {
      id: operationId,
      type: 'deduct',
      amount,
      timestamp: Date.now(),
      promise: fetch('/api/user/credits/deduct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          credits: amount,
          transType,
          description,
        }),
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
    };

    // 添加到待处理操作列表
    setPendingOperations(prev => [...prev, operation]);

    try {
      const result = await operation.promise;
      
      if (result.code !== 0) {
        throw new Error(result.message || 'Deduction failed');
      }

      // 成功：移除待处理操作，UI已经是正确的
      setPendingOperations(prev => prev.filter(op => op.id !== operationId));
      
      // 可选：使用服务器返回的精确积分值
      if (result.data?.remainingCredits !== undefined) {
        setCredits(prev => prev ? {
          ...prev,
          left_credits: result.data.remainingCredits
        } : null);
      }

      return true;

    } catch (error) {
      console.error('Credit deduction failed:', error);
      
      // 失败：回滚UI状态
      setCredits(originalCredits);
      setPendingOperations(prev => prev.filter(op => op.id !== operationId));
      
      toast.error("积分扣除失败，请重试");
      return false;
    }
  }, [credits]);

  // 乐观增加积分
  const optimisticAdd = useCallback(async (
    amount: number, 
    reason?: string
  ): Promise<boolean> => {
    if (!credits) return false;

    const operationId = `add_${Date.now()}_${Math.random()}`;
    
    // 立即更新UI
    const originalCredits = credits;
    setCredits(prev => prev ? {
      ...prev,
      left_credits: prev.left_credits + amount,
      is_pro: true
    } : null);

    // 这里应该调用增加积分的API（如果有的话）
    // 目前只是模拟成功
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 100));
      
      toast.success(`成功获得 ${amount} 积分`);
      return true;
      
    } catch (error) {
      console.error('Credit addition failed:', error);
      
      // 失败：回滚UI状态
      setCredits(originalCredits);
      toast.error("积分增加失败");
      return false;
    }
  }, [credits]);

  // 乐观退款
  const optimisticRefund = useCallback(async (
    amount: number, 
    originalTransactionId: string, 
    reason?: string
  ): Promise<boolean> => {
    if (!credits) return false;

    const operationId = `refund_${Date.now()}_${Math.random()}`;
    
    // 立即更新UI
    const originalCredits = credits;
    setCredits(prev => prev ? {
      ...prev,
      left_credits: prev.left_credits + amount,
      is_pro: true
    } : null);

    // 创建后台操作
    const operation: PendingOperation = {
      id: operationId,
      type: 'refund',
      amount,
      timestamp: Date.now(),
      promise: fetch('/api/user/credits/refund', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          credits: amount,
          originalTransactionId,
          reason,
        }),
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
    };

    setPendingOperations(prev => [...prev, operation]);

    try {
      const result = await operation.promise;
      
      if (result.code !== 0) {
        throw new Error(result.message || 'Refund failed');
      }

      // 成功：移除待处理操作
      setPendingOperations(prev => prev.filter(op => op.id !== operationId));
      toast.success(`成功退款 ${amount} 积分`);
      return true;

    } catch (error) {
      console.error('Credit refund failed:', error);
      
      // 失败：回滚UI状态
      setCredits(originalCredits);
      setPendingOperations(prev => prev.filter(op => op.id !== operationId));
      
      toast.error("积分退款失败");
      return false;
    }
  }, [credits]);

  // 刷新积分
  const refreshCredits = useCallback(() => {
    fetchCredits();
  }, [fetchCredits]);

  // 清除错误状态
  const clearError = useCallback(() => {
    setError(false);
  }, []);

  // 初始化和定期刷新
  useEffect(() => {
    if (user) {
      fetchCredits();
    } else {
      setCredits(null);
      setError(false);
      setPendingOperations([]);
    }
  }, [user, fetchCredits]);

  // 定期同步（30秒）
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      if (!document.hidden && pendingOperations.length === 0) {
        fetchCredits();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [user, fetchCredits, pendingOperations.length]);

  // 页面可见性变化时同步
  useEffect(() => {
    if (!user) return;

    const handleVisibilityChange = () => {
      if (!document.hidden && pendingOperations.length === 0) {
        fetchCredits();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user, fetchCredits, pendingOperations.length]);

  const value: CreditsContextType = {
    credits,
    loading,
    error,
    pendingOperations,
    fetchCredits,
    optimisticDeduct,
    optimisticAdd,
    optimisticRefund,
    refreshCredits,
    clearError,
  };

  return (
    <CreditsContext.Provider value={value}>
      {children}
    </CreditsContext.Provider>
  );
}

export function useCredits() {
  const context = useContext(CreditsContext);
  if (context === undefined) {
    throw new Error('useCredits must be used within a CreditsProvider');
  }
  return context;
}
