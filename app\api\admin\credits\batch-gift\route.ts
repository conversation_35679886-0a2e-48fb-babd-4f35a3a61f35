import { NextRequest } from "next/server";
import { getUserInfo } from "@/services/user";
import { findUserByEmail, findUsersByEmails } from "@/models/user";
import { batchGiftCredits, BatchGiftOperation } from "@/services/credit";
import { respData, respErr } from "@/lib/resp";
import { getOneYearLaterTimestr } from "@/lib/time";

/**
 * POST /api/admin/credits/batch-gift
 * Admin batch gift credits to multiple users
 */
export async function POST(req: NextRequest) {
  try {
    // 验证管理员权限
    const adminInfo = await getUserInfo();
    if (!adminInfo || !adminInfo.email) {
      return respErr("Unauthorized", 401);
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(adminInfo.email)) {
      return respErr("Access denied", 403);
    }

    // 解析请求参数
    const { operations, defaultExpiredAt, defaultReason } = await req.json();

    // 验证参数
    if (!Array.isArray(operations) || operations.length === 0) {
      return respErr("Operations array is required and cannot be empty", 400);
    }

    if (operations.length > 100) {
      return respErr("Cannot process more than 100 operations at once", 400);
    }

    // 验证每个操作的参数
    const validatedOperations: BatchGiftOperation[] = [];
    const userEmails: string[] = [];

    for (let i = 0; i < operations.length; i++) {
      const op = operations[i];
      
      if (!op.userEmail || typeof op.userEmail !== 'string') {
        return respErr(`Operation ${i}: User email is required`, 400);
      }

      if (typeof op.credits !== 'number' || op.credits <= 0) {
        return respErr(`Operation ${i}: Invalid credits amount`, 400);
      }

      if (op.credits > 10000) {
        return respErr(`Operation ${i}: Credits amount cannot exceed 10000`, 400);
      }

      userEmails.push(op.userEmail);
      validatedOperations.push({
        userEmail: op.userEmail,
        credits: op.credits,
        reason: op.reason || defaultReason || "Batch admin gift",
        expiredAt: op.expiredAt || defaultExpiredAt || getOneYearLaterTimestr()
      });
    }

    // 批量查找用户
    console.log(`Looking up ${userEmails.length} users for batch gift operation...`);
    const users = await findUsersByEmails(userEmails);
    const userMap = new Map(users.map(user => [user.email, user]));

    // 检查所有用户是否存在，并添加 userUuid
    const finalOperations: BatchGiftOperation[] = [];
    const notFoundUsers: string[] = [];

    for (const operation of validatedOperations) {
      const user = userMap.get(operation.userEmail);
      if (!user || !user.uuid) {
        notFoundUsers.push(operation.userEmail);
        continue;
      }

      finalOperations.push({
        ...operation,
        userUuid: user.uuid
      });
    }

    // 如果有用户不存在，返回错误
    if (notFoundUsers.length > 0) {
      return respErr(`Users not found: ${notFoundUsers.join(', ')}`, 404);
    }

    // 执行批量赠送积分操作
    console.log(`Starting batch gift operation for ${finalOperations.length} users...`);
    const startTime = Date.now();
    
    const result = await batchGiftCredits(finalOperations, adminInfo.email);
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // 记录操作日志
    console.log(`Batch gift completed by ${adminInfo.email}: ${result.successfulOperations}/${result.totalOperations} successful in ${totalTime}ms`);

    // 计算统计信息
    const totalCreditsGifted = result.results
      .filter(r => r.success)
      .reduce((sum, r) => sum + (r.credits || 0), 0);

    const response = {
      success: result.success,
      message: result.success 
        ? "Batch gift operation completed successfully" 
        : "Batch gift operation completed with some failures",
      summary: {
        totalOperations: result.totalOperations,
        successfulOperations: result.successfulOperations,
        failedOperations: result.failedOperations,
        totalCreditsGifted,
        executionTime: totalTime,
        cacheWarmedUp: result.cacheWarmedUp,
        averageTimePerOperation: result.totalOperations > 0 
          ? (totalTime / result.totalOperations).toFixed(2) 
          : 0
      },
      details: {
        adminEmail: adminInfo.email,
        timestamp: new Date().toISOString(),
        operations: result.results
      }
    };

    return respData(response);

  } catch (error) {
    console.error("Batch gift credits error:", error);
    return respErr("Internal server error", 500);
  }
}

/**
 * GET /api/admin/credits/batch-gift
 * Get batch gift operation status and history
 */
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const adminInfo = await getUserInfo();
    if (!adminInfo || !adminInfo.email) {
      return respErr("Unauthorized", 401);
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(adminInfo.email)) {
      return respErr("Access denied", 403);
    }

    // 返回批量操作的使用指南和限制
    return respData({
      batchGiftInfo: {
        maxOperationsPerBatch: 100,
        maxCreditsPerOperation: 10000,
        supportedFeatures: [
          "Cache warmup for improved performance",
          "Batch user lookup",
          "Parallel credit operations",
          "Detailed operation results",
          "Performance metrics"
        ],
        usage: {
          endpoint: "/api/admin/credits/batch-gift",
          method: "POST",
          requestFormat: {
            operations: [
              {
                userEmail: "<EMAIL>",
                credits: 100,
                reason: "Optional reason",
                expiredAt: "Optional ISO date string"
              }
            ],
            defaultExpiredAt: "Optional default expiry for all operations",
            defaultReason: "Optional default reason for all operations"
          }
        },
        performanceOptimizations: [
          "Cache prewarming reduces database queries",
          "Batch user lookup minimizes API calls",
          "Parallel processing improves throughput",
          "Optimized cache updates after operations"
        ]
      }
    });

  } catch (error) {
    console.error("Get batch gift info error:", error);
    return respErr("Internal server error", 500);
  }
}
