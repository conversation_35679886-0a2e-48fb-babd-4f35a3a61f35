-- 积分系统数据库索引优化脚本
-- 根据 docs/credits-system-optimization.md 的要求添加关键索引

-- ============================================================================
-- 1. 用户积分查询优化索引（最常用的查询）
-- ============================================================================

-- 主要用于 getUserValidCredits 函数的查询优化
-- 查询模式: SELECT * FROM credits WHERE user_uuid = ? AND expired_at >= ? ORDER BY expired_at ASC
CREATE INDEX IF NOT EXISTS idx_credits_user_expired_credits 
ON credits (user_uuid, expired_at, credits);

-- 说明：
-- - user_uuid: 用户筛选，最高选择性
-- - expired_at: 过期时间筛选和排序
-- - credits: 包含列，避免回表查询
-- 预期效果：单用户积分查询时间从 200ms 降至 50ms

-- ============================================================================
-- 2. 用户交易记录查询优化索引
-- ============================================================================

-- 主要用于查询用户的交易历史记录
-- 查询模式: SELECT * FROM credits WHERE user_uuid = ? ORDER BY created_at DESC
CREATE INDEX IF NOT EXISTS idx_credits_user_created 
ON credits (user_uuid, created_at DESC);

-- 说明：
-- - user_uuid: 用户筛选
-- - created_at DESC: 按创建时间倒序排列（最新的在前）
-- 预期效果：用户交易记录查询性能提升 5-10 倍

-- ============================================================================
-- 3. 管理员统计查询优化索引
-- ============================================================================

-- 主要用于管理员页面的统计查询
-- 查询模式: SELECT COUNT(*), SUM(credits) FROM credits WHERE created_at >= ? AND trans_type = ?
CREATE INDEX IF NOT EXISTS idx_credits_created_type 
ON credits (created_at, trans_type);

-- 说明：
-- - created_at: 时间范围筛选
-- - trans_type: 交易类型筛选
-- 预期效果：管理员统计查询速度显著提升

-- ============================================================================
-- 4. 交易号查询优化索引（已存在，但确保存在）
-- ============================================================================

-- 主要用于 findCreditByTransNo 函数
-- 查询模式: SELECT * FROM credits WHERE trans_no = ?
-- 注意：trans_no 已经有 UNIQUE 约束，会自动创建索引，这里只是确认

-- 检查是否存在 trans_no 的唯一索引
-- 如果不存在则创建（通常不需要，因为 UNIQUE 约束会自动创建）
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'credits'
        AND n.nspname = 'public'
        AND EXISTS (
            SELECT 1 FROM pg_index i
            JOIN pg_attribute a ON a.attrelid = c.oid AND a.attnum = ANY(i.indkey)
            WHERE i.indrelid = c.oid AND a.attname = 'trans_no'
        )
    ) THEN
        CREATE UNIQUE INDEX IF NOT EXISTS idx_credits_trans_no ON credits (trans_no);
    END IF;
END $$;

-- ============================================================================
-- 5. 批量查询优化索引
-- ============================================================================

-- 主要用于 getBatchUserValidCredits 函数的批量查询优化
-- 查询模式: SELECT * FROM credits WHERE user_uuid IN (?, ?, ...) AND expired_at >= ?
-- 注意：idx_credits_user_expired_credits 已经可以优化这个查询

-- 为了进一步优化批量查询，添加一个专门的索引
CREATE INDEX IF NOT EXISTS idx_credits_batch_query 
ON credits (expired_at, user_uuid, credits);

-- 说明：
-- - expired_at: 首先过滤有效积分
-- - user_uuid: 然后按用户分组
-- - credits: 包含列，避免回表
-- 预期效果：批量查询性能提升 5-10 倍

-- ============================================================================
-- 6. 性能监控和统计信息更新
-- ============================================================================

-- 更新表的统计信息，帮助查询优化器做出更好的执行计划
ANALYZE credits;

-- ============================================================================
-- 7. 索引使用情况查询（用于监控）
-- ============================================================================

-- 创建一个视图来监控索引使用情况
CREATE OR REPLACE VIEW credits_index_usage AS
SELECT
    schemaname,
    relname as tablename,
    indexrelname as indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
WHERE relname = 'credits'
ORDER BY idx_scan DESC;

-- ============================================================================
-- 8. 查询性能测试脚本
-- ============================================================================

-- 创建一个函数来测试查询性能
CREATE OR REPLACE FUNCTION test_credits_query_performance()
RETURNS TABLE(
    query_type TEXT,
    execution_time_ms NUMERIC,
    rows_returned BIGINT
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    test_user_uuid VARCHAR(255);
    row_count BIGINT;
BEGIN
    -- 获取一个测试用户UUID
    SELECT user_uuid INTO test_user_uuid 
    FROM credits 
    LIMIT 1;
    
    IF test_user_uuid IS NULL THEN
        RAISE NOTICE 'No test data available in credits table';
        RETURN;
    END IF;
    
    -- 测试1: 单用户积分查询
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count
    FROM credits 
    WHERE user_uuid = test_user_uuid 
    AND expired_at >= NOW()
    ORDER BY expired_at ASC;
    end_time := clock_timestamp();
    
    query_type := 'Single User Credits Query';
    execution_time_ms := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    rows_returned := row_count;
    RETURN NEXT;
    
    -- 测试2: 用户交易记录查询
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count
    FROM credits 
    WHERE user_uuid = test_user_uuid 
    ORDER BY created_at DESC
    LIMIT 50;
    end_time := clock_timestamp();
    
    query_type := 'User Transaction History';
    execution_time_ms := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    rows_returned := row_count;
    RETURN NEXT;
    
    -- 测试3: 管理员统计查询
    start_time := clock_timestamp();
    SELECT COUNT(*) INTO row_count
    FROM credits 
    WHERE created_at >= (NOW() - INTERVAL '30 days')
    AND trans_type = 'image_generation';
    end_time := clock_timestamp();
    
    query_type := 'Admin Statistics Query';
    execution_time_ms := EXTRACT(MILLISECONDS FROM (end_time - start_time));
    rows_returned := row_count;
    RETURN NEXT;
    
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 执行完成提示
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '✅ 积分系统数据库索引优化完成！';
    RAISE NOTICE '📊 已创建的索引：';
    RAISE NOTICE '  - idx_credits_user_expired_credits: 用户积分查询优化';
    RAISE NOTICE '  - idx_credits_user_created: 用户交易记录查询优化';
    RAISE NOTICE '  - idx_credits_created_type: 管理员统计查询优化';
    RAISE NOTICE '  - idx_credits_batch_query: 批量查询优化';
    RAISE NOTICE '🔍 使用 SELECT * FROM credits_index_usage; 查看索引使用情况';
    RAISE NOTICE '⚡ 使用 SELECT * FROM test_credits_query_performance(); 测试查询性能';
END $$;
