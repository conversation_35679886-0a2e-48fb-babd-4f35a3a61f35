"use client";

import { Textarea } from "@/components/ui/textarea";
import { Upload } from "lucide-react";
import { AIGenerator as AIGeneratorType, AIGeneratorTranslations } from "@/types/blocks/ai-generator";

interface InputSectionProps {
  mode: "text-to-image" | "image-to-image";
  // Text to image props
  textPrompt: string;
  setTextPrompt: (value: string) => void;
  generator: AIGeneratorType;
  // Image to image props
  imagePrompt: string;
  setImagePrompt: (value: string) => void;
  uploadedImage: string | null;
  handleImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  translations?: AIGeneratorTranslations;
}

export default function InputSection({
  mode,
  textPrompt,
  setTextPrompt,
  generator,
  imagePrompt,
  setImagePrompt,
  uploadedImage,
  handleImageUpload,
  translations
}: InputSectionProps) {
  if (mode === "text-to-image") {
    return (
      <div className="content-layout h-[400px] flex flex-col">
        <div className="input-section p-6 h-[400px]" data-text-input>
          <Textarea
            placeholder={generator.text_to_image?.placeholder || "描述你想要生成的图片..."}
            value={textPrompt}
            onChange={(e) => setTextPrompt(e.target.value)}
            className="h-full resize-none border-0 bg-transparent text-2xl md:text-3xl text-white placeholder:text-white/60 focus-visible:ring-0 focus:ring-0 focus:outline-none focus-visible:outline-none focus:border-0 focus-visible:border-0 outline-none ring-0 p-0"
            style={{ outline: 'none', boxShadow: 'none' }}
          />
        </div>
      </div>
    );
  }

  // Image to Image mode
  return (
    <div className="content-layout grid grid-cols-1 lg:grid-cols-2">
      <div className="input-section h-[400px] p-6 border-r border-white/20 flex items-center justify-center" data-upload-area>
        <input
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
          id="image-upload"
        />
        <label
          htmlFor="image-upload"
          className="cursor-pointer w-full h-full flex items-center justify-center border-2 border-dashed border-white/25 rounded-xl hover:border-white/50 transition-colors"
        >
          {uploadedImage ? (
            <img
              src={uploadedImage}
              alt="Uploaded"
              className="w-full h-full object-cover rounded-xl"
            />
          ) : (
            <div className="text-center text-white">
              <Upload className="h-16 w-16 mx-auto mb-4" />
              <p className="text-xl font-medium">{generator.image_to_image?.upload_text || "点击上传图片"}</p>
              <p className="text-base mt-2 text-white/70">{translations?.placeholders?.file_support || "支持 JPG, PNG, WebP"}</p>
            </div>
          )}
        </label>
      </div>

      <div className="text-input-section flex flex-col h-[400px]">
        <div className="p-6 h-[400px]" data-text-input>
          <Textarea
            placeholder={translations?.placeholders?.image_modification || "描述你想要对图片进行的修改..."}
            value={imagePrompt}
            onChange={(e) => setImagePrompt(e.target.value)}
            className="h-full resize-none border-0 bg-transparent text-2xl md:text-3xl text-white placeholder:text-white/60 focus-visible:ring-0 focus:ring-0 focus:outline-none focus-visible:outline-none focus:border-0 focus-visible:border-0 outline-none ring-0 p-0"
            style={{ outline: 'none', boxShadow: 'none' }}
          />
        </div>
      </div>
    </div>
  );
}
