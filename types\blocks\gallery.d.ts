export interface ImageData {
  id: string;
  src: string;
  alt: string;
  width: number;
  height: number;
  aspectRatio: number;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  prompt?: string;
  tags?: string[];
}

// SSR版本的图片数据 - 不包含src，用于服务端渲染
export interface ImageDataSSR {
  id: string;
  alt: string;
  width: number;
  height: number;
  aspectRatio: number;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  prompt?: string;
  tags?: string[];
}

// 客户端版本的图片数据 - 包含完整信息
export interface ImageDataCSR extends ImageDataSSR {
  src: string;
}

// 图片加载状态
export interface ImageLoadState {
  id: string;
  isLoaded: boolean;
  hasError: boolean;
  src?: string;
}

export interface GalleryTranslations {
  title?: string;
  subtitle?: string;
  loading?: string;
  error?: string;
  retry?: string;
  close?: string;
  image_details?: string;
  style?: string;
  color?: string;
  lighting?: string;
  composition?: string;
  prompt?: string;
  no_data?: string;
  generation_parameters?: string;
  loading_images?: string;
  image_loading?: string;
  tags?: string;
}

export interface GallerySection {
  title?: string;
  subtitle?: string;
  images?: ImageData[];
  translations?: GalleryTranslations;
}

// SSR版本的Gallery Section
export interface GallerySectionSSR {
  title?: string;
  subtitle?: string;
  images?: ImageDataSSR[];
  translations?: GalleryTranslations;
}

// 渲染模式
export type GalleryRenderMode = 'ssr' | 'csr' | 'hybrid';

// Gallery组件的Props
export interface GalleryProps {
  gallery: GallerySection;
  className?: string;
  renderMode?: GalleryRenderMode;
}

// SSR Gallery组件的Props
export interface SSRGalleryProps {
  gallery: GallerySectionSSR;
  className?: string;
}

// Client Gallery组件的Props
export interface ClientGalleryProps {
  images: ImageDataSSR[];
  translations?: GalleryTranslations;
  onImageClick?: (image: ImageDataCSR) => void;
}