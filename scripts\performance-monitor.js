/**
 * 积分系统性能监控脚本
 * 用于生产环境的性能监控和报告
 */

const { performance } = require('perf_hooks');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      cacheHitRate: [],
      responseTime: [],
      databaseQueryTime: [],
      batchOperationTime: [],
      errorRate: []
    };
    this.alerts = [];
    this.thresholds = {
      cacheHitRate: 80, // 最低80%
      responseTime: 200, // 最高200ms
      databaseQueryTime: 100, // 最高100ms
      batchOperationTime: 5000, // 最高5秒
      errorRate: 5 // 最高5%
    };
  }

  // 记录缓存性能
  recordCachePerformance(hits, misses) {
    const total = hits + misses;
    const hitRate = total > 0 ? (hits / total) * 100 : 0;
    
    this.metrics.cacheHitRate.push({
      timestamp: new Date().toISOString(),
      hitRate,
      hits,
      misses,
      total
    });

    if (hitRate < this.thresholds.cacheHitRate) {
      this.addAlert('cache_hit_rate', `缓存命中率过低: ${hitRate.toFixed(2)}%`);
    }

    return hitRate;
  }

  // 记录响应时间
  recordResponseTime(operation, time) {
    this.metrics.responseTime.push({
      timestamp: new Date().toISOString(),
      operation,
      time
    });

    if (time > this.thresholds.responseTime) {
      this.addAlert('response_time', `${operation} 响应时间过长: ${time}ms`);
    }

    return time;
  }

  // 记录数据库查询时间
  recordDatabaseQueryTime(queryType, time) {
    this.metrics.databaseQueryTime.push({
      timestamp: new Date().toISOString(),
      queryType,
      time
    });

    if (time > this.thresholds.databaseQueryTime) {
      this.addAlert('database_query', `${queryType} 查询时间过长: ${time}ms`);
    }

    return time;
  }

  // 记录批量操作时间
  recordBatchOperationTime(operationCount, time) {
    const avgTime = time / operationCount;
    
    this.metrics.batchOperationTime.push({
      timestamp: new Date().toISOString(),
      operationCount,
      totalTime: time,
      avgTime
    });

    if (time > this.thresholds.batchOperationTime) {
      this.addAlert('batch_operation', `批量操作时间过长: ${time}ms (${operationCount}个操作)`);
    }

    return { totalTime: time, avgTime };
  }

  // 记录错误率
  recordErrorRate(totalOperations, errors) {
    const errorRate = totalOperations > 0 ? (errors / totalOperations) * 100 : 0;
    
    this.metrics.errorRate.push({
      timestamp: new Date().toISOString(),
      totalOperations,
      errors,
      errorRate
    });

    if (errorRate > this.thresholds.errorRate) {
      this.addAlert('error_rate', `错误率过高: ${errorRate.toFixed(2)}%`);
    }

    return errorRate;
  }

  // 添加告警
  addAlert(type, message) {
    this.alerts.push({
      timestamp: new Date().toISOString(),
      type,
      message,
      severity: this.getAlertSeverity(type)
    });

    console.warn(`⚠️  [${type.toUpperCase()}] ${message}`);
  }

  // 获取告警严重程度
  getAlertSeverity(type) {
    const severityMap = {
      cache_hit_rate: 'medium',
      response_time: 'high',
      database_query: 'medium',
      batch_operation: 'low',
      error_rate: 'high'
    };
    return severityMap[type] || 'low';
  }

  // 生成性能报告
  generateReport(timeRange = '1h') {
    const now = new Date();
    const cutoff = new Date(now.getTime() - this.parseTimeRange(timeRange));

    console.log('📊 积分系统性能监控报告');
    console.log('=' .repeat(50));
    console.log(`时间范围: ${timeRange}`);
    console.log(`生成时间: ${now.toISOString()}`);
    console.log();

    // 缓存性能报告
    this.reportCachePerformance(cutoff);
    
    // 响应时间报告
    this.reportResponseTime(cutoff);
    
    // 数据库性能报告
    this.reportDatabasePerformance(cutoff);
    
    // 批量操作报告
    this.reportBatchOperations(cutoff);
    
    // 错误率报告
    this.reportErrorRate(cutoff);
    
    // 告警汇总
    this.reportAlerts(cutoff);
    
    // 健康状态评估
    this.reportHealthStatus();
  }

  // 缓存性能报告
  reportCachePerformance(cutoff) {
    const recentData = this.metrics.cacheHitRate.filter(
      item => new Date(item.timestamp) > cutoff
    );

    if (recentData.length === 0) {
      console.log('📈 缓存性能: 无数据');
      return;
    }

    const avgHitRate = recentData.reduce((sum, item) => sum + item.hitRate, 0) / recentData.length;
    const totalHits = recentData.reduce((sum, item) => sum + item.hits, 0);
    const totalMisses = recentData.reduce((sum, item) => sum + item.misses, 0);

    console.log('📈 缓存性能:');
    console.log(`  平均命中率: ${avgHitRate.toFixed(2)}%`);
    console.log(`  总命中次数: ${totalHits}`);
    console.log(`  总未命中次数: ${totalMisses}`);
    console.log(`  状态: ${avgHitRate >= this.thresholds.cacheHitRate ? '✅ 正常' : '⚠️  异常'}`);
    console.log();
  }

  // 响应时间报告
  reportResponseTime(cutoff) {
    const recentData = this.metrics.responseTime.filter(
      item => new Date(item.timestamp) > cutoff
    );

    if (recentData.length === 0) {
      console.log('⚡ 响应时间: 无数据');
      return;
    }

    const avgTime = recentData.reduce((sum, item) => sum + item.time, 0) / recentData.length;
    const maxTime = Math.max(...recentData.map(item => item.time));
    const minTime = Math.min(...recentData.map(item => item.time));

    console.log('⚡ 响应时间:');
    console.log(`  平均响应时间: ${avgTime.toFixed(2)}ms`);
    console.log(`  最大响应时间: ${maxTime}ms`);
    console.log(`  最小响应时间: ${minTime}ms`);
    console.log(`  状态: ${avgTime <= this.thresholds.responseTime ? '✅ 正常' : '⚠️  异常'}`);
    console.log();
  }

  // 数据库性能报告
  reportDatabasePerformance(cutoff) {
    const recentData = this.metrics.databaseQueryTime.filter(
      item => new Date(item.timestamp) > cutoff
    );

    if (recentData.length === 0) {
      console.log('🗄️  数据库性能: 无数据');
      return;
    }

    const avgTime = recentData.reduce((sum, item) => sum + item.time, 0) / recentData.length;
    const queryTypes = [...new Set(recentData.map(item => item.queryType))];

    console.log('🗄️  数据库性能:');
    console.log(`  平均查询时间: ${avgTime.toFixed(2)}ms`);
    console.log(`  查询类型数: ${queryTypes.length}`);
    console.log(`  总查询次数: ${recentData.length}`);
    console.log(`  状态: ${avgTime <= this.thresholds.databaseQueryTime ? '✅ 正常' : '⚠️  异常'}`);
    console.log();
  }

  // 批量操作报告
  reportBatchOperations(cutoff) {
    const recentData = this.metrics.batchOperationTime.filter(
      item => new Date(item.timestamp) > cutoff
    );

    if (recentData.length === 0) {
      console.log('📦 批量操作: 无数据');
      return;
    }

    const avgTotalTime = recentData.reduce((sum, item) => sum + item.totalTime, 0) / recentData.length;
    const avgOperationTime = recentData.reduce((sum, item) => sum + item.avgTime, 0) / recentData.length;
    const totalOperations = recentData.reduce((sum, item) => sum + item.operationCount, 0);

    console.log('📦 批量操作:');
    console.log(`  平均批量时间: ${avgTotalTime.toFixed(2)}ms`);
    console.log(`  平均单操作时间: ${avgOperationTime.toFixed(2)}ms`);
    console.log(`  总操作数: ${totalOperations}`);
    console.log(`  状态: ${avgTotalTime <= this.thresholds.batchOperationTime ? '✅ 正常' : '⚠️  异常'}`);
    console.log();
  }

  // 错误率报告
  reportErrorRate(cutoff) {
    const recentData = this.metrics.errorRate.filter(
      item => new Date(item.timestamp) > cutoff
    );

    if (recentData.length === 0) {
      console.log('❌ 错误率: 无数据');
      return;
    }

    const avgErrorRate = recentData.reduce((sum, item) => sum + item.errorRate, 0) / recentData.length;
    const totalOperations = recentData.reduce((sum, item) => sum + item.totalOperations, 0);
    const totalErrors = recentData.reduce((sum, item) => sum + item.errors, 0);

    console.log('❌ 错误率:');
    console.log(`  平均错误率: ${avgErrorRate.toFixed(2)}%`);
    console.log(`  总操作数: ${totalOperations}`);
    console.log(`  总错误数: ${totalErrors}`);
    console.log(`  状态: ${avgErrorRate <= this.thresholds.errorRate ? '✅ 正常' : '⚠️  异常'}`);
    console.log();
  }

  // 告警汇总
  reportAlerts(cutoff) {
    const recentAlerts = this.alerts.filter(
      alert => new Date(alert.timestamp) > cutoff
    );

    console.log('🚨 告警汇总:');
    if (recentAlerts.length === 0) {
      console.log('  无告警 ✅');
    } else {
      const severityCounts = recentAlerts.reduce((counts, alert) => {
        counts[alert.severity] = (counts[alert.severity] || 0) + 1;
        return counts;
      }, {});

      console.log(`  总告警数: ${recentAlerts.length}`);
      console.log(`  高级告警: ${severityCounts.high || 0}`);
      console.log(`  中级告警: ${severityCounts.medium || 0}`);
      console.log(`  低级告警: ${severityCounts.low || 0}`);

      console.log('\n  最近告警:');
      recentAlerts.slice(-5).forEach(alert => {
        console.log(`    [${alert.severity.toUpperCase()}] ${alert.message}`);
      });
    }
    console.log();
  }

  // 健康状态评估
  reportHealthStatus() {
    const recentAlerts = this.alerts.filter(
      alert => new Date(alert.timestamp) > new Date(Date.now() - 3600000) // 最近1小时
    );

    const highSeverityAlerts = recentAlerts.filter(alert => alert.severity === 'high').length;
    const mediumSeverityAlerts = recentAlerts.filter(alert => alert.severity === 'medium').length;

    let healthStatus = 'healthy';
    let healthMessage = '系统运行正常';

    if (highSeverityAlerts > 0) {
      healthStatus = 'critical';
      healthMessage = `发现 ${highSeverityAlerts} 个高级告警，需要立即处理`;
    } else if (mediumSeverityAlerts > 3) {
      healthStatus = 'warning';
      healthMessage = `发现 ${mediumSeverityAlerts} 个中级告警，建议关注`;
    } else if (recentAlerts.length > 10) {
      healthStatus = 'warning';
      healthMessage = `告警数量较多 (${recentAlerts.length})，建议检查`;
    }

    const statusEmoji = {
      healthy: '💚',
      warning: '💛',
      critical: '❤️'
    };

    console.log('🏥 系统健康状态:');
    console.log(`  状态: ${statusEmoji[healthStatus]} ${healthStatus.toUpperCase()}`);
    console.log(`  描述: ${healthMessage}`);
    console.log();
  }

  // 解析时间范围
  parseTimeRange(timeRange) {
    const units = {
      'm': 60 * 1000,
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000
    };

    const match = timeRange.match(/^(\d+)([mhd])$/);
    if (!match) return 3600000; // 默认1小时

    const [, amount, unit] = match;
    return parseInt(amount) * units[unit];
  }

  // 模拟监控数据（用于演示）
  simulateMonitoringData() {
    console.log('🔄 生成模拟监控数据...\n');

    // 模拟缓存性能数据
    for (let i = 0; i < 10; i++) {
      const hits = 80 + Math.floor(Math.random() * 15);
      const misses = 20 - Math.floor(Math.random() * 15);
      this.recordCachePerformance(hits, misses);
    }

    // 模拟响应时间数据
    const operations = ['getUserCredits', 'deductCredits', 'batchGift'];
    for (let i = 0; i < 20; i++) {
      const operation = operations[Math.floor(Math.random() * operations.length)];
      const time = 50 + Math.floor(Math.random() * 100);
      this.recordResponseTime(operation, time);
    }

    // 模拟数据库查询时间
    const queryTypes = ['userCredits', 'userTransactions', 'adminStats'];
    for (let i = 0; i < 15; i++) {
      const queryType = queryTypes[Math.floor(Math.random() * queryTypes.length)];
      const time = 30 + Math.floor(Math.random() * 50);
      this.recordDatabaseQueryTime(queryType, time);
    }

    // 模拟批量操作
    for (let i = 0; i < 5; i++) {
      const operationCount = 10 + Math.floor(Math.random() * 40);
      const time = operationCount * (20 + Math.random() * 30);
      this.recordBatchOperationTime(operationCount, time);
    }

    // 模拟错误率
    for (let i = 0; i < 8; i++) {
      const totalOps = 100 + Math.floor(Math.random() * 200);
      const errors = Math.floor(Math.random() * 5);
      this.recordErrorRate(totalOps, errors);
    }
  }
}

// 运行性能监控演示
function runPerformanceMonitorDemo() {
  console.log('🚀 积分系统性能监控演示\n');

  const monitor = new PerformanceMonitor();
  
  // 生成模拟数据
  monitor.simulateMonitoringData();
  
  // 生成报告
  monitor.generateReport('1h');
  
  console.log('📝 监控演示完成！');
  console.log('💡 在生产环境中，这些数据将来自实际的系统指标');
}

// 运行演示
runPerformanceMonitorDemo();
