/**
 * 积分操作 Hook
 * 提供乐观更新的积分操作方法
 */

import { useCredits } from '@/contexts/credits';
import { useCallback } from 'react';
import { toast } from 'sonner';

export interface CreditOperationOptions {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useCreditsOperations() {
  const { 
    credits, 
    optimisticDeduct, 
    optimisticAdd, 
    optimisticRefund,
    refreshCredits 
  } = useCredits();

  // 图片生成扣费
  const deductForImageGeneration = useCallback(async (
    imageCount: number,
    highQuality: boolean = false,
    options: CreditOperationOptions = {}
  ) => {
    const baseCredits = 1; // 基础积分
    const qualityMultiplier = highQuality ? 2 : 1;
    const totalCredits = baseCredits * imageCount * qualityMultiplier;

    const success = await optimisticDeduct(
      totalCredits,
      'image_generation',
      `生成${imageCount}张${highQuality ? '高质量' : '标准'}图片`
    );

    if (success) {
      options.showSuccessToast && toast.success(`成功扣除 ${totalCredits} 积分`);
      options.onSuccess?.();
    } else {
      options.showErrorToast && toast.error('积分扣除失败');
      options.onError?.(new Error('Credit deduction failed'));
    }

    return success;
  }, [optimisticDeduct]);

  // API 调用扣费
  const deductForApiCall = useCallback(async (
    apiType: string = 'ping',
    cost: number = 1,
    options: CreditOperationOptions = {}
  ) => {
    const success = await optimisticDeduct(
      cost,
      apiType,
      `API调用: ${apiType}`
    );

    if (success) {
      options.showSuccessToast && toast.success(`API调用成功，扣除 ${cost} 积分`);
      options.onSuccess?.();
    } else {
      options.showErrorToast && toast.error('积分不足，API调用失败');
      options.onError?.(new Error('Insufficient credits for API call'));
    }

    return success;
  }, [optimisticDeduct]);

  // 操作失败退款
  const refundForFailedOperation = useCallback(async (
    amount: number,
    originalTransactionId: string,
    reason: string = '操作失败退款',
    options: CreditOperationOptions = {}
  ) => {
    const success = await optimisticRefund(amount, originalTransactionId, reason);

    if (success) {
      options.showSuccessToast && toast.success(`操作失败，已退还 ${amount} 积分`);
      options.onSuccess?.();
    } else {
      options.showErrorToast && toast.error('退款失败，请联系客服');
      options.onError?.(new Error('Refund failed'));
    }

    return success;
  }, [optimisticRefund]);

  // 管理员赠送积分（前端显示用）
  const addGiftCredits = useCallback(async (
    amount: number,
    reason: string = '管理员赠送',
    options: CreditOperationOptions = {}
  ) => {
    const success = await optimisticAdd(amount, reason);

    if (success) {
      options.showSuccessToast && toast.success(`获得 ${amount} 积分奖励！`);
      options.onSuccess?.();
    } else {
      options.showErrorToast && toast.error('积分发放失败');
      options.onError?.(new Error('Credit gift failed'));
    }

    return success;
  }, [optimisticAdd]);

  // 检查积分是否足够
  const checkSufficientCredits = useCallback((requiredCredits: number): boolean => {
    if (!credits) return false;
    return credits.left_credits >= requiredCredits;
  }, [credits]);

  // 计算图片生成所需积分
  const calculateImageGenerationCredits = useCallback((
    imageCount: number,
    highQuality: boolean = false
  ): number => {
    const baseCredits = 1;
    const qualityMultiplier = highQuality ? 2 : 1;
    return baseCredits * imageCount * qualityMultiplier;
  }, []);

  // 获取当前积分状态
  const getCurrentCredits = useCallback(() => {
    return credits?.left_credits || 0;
  }, [credits]);

  // 是否为付费用户
  const isPro = useCallback(() => {
    return credits?.is_pro || false;
  }, [credits]);

  // 是否已充值
  const isRecharged = useCallback(() => {
    return credits?.is_recharged || false;
  }, [credits]);

  return {
    // 积分信息
    currentCredits: getCurrentCredits(),
    isPro: isPro(),
    isRecharged: isRecharged(),
    
    // 操作方法
    deductForImageGeneration,
    deductForApiCall,
    refundForFailedOperation,
    addGiftCredits,
    
    // 工具方法
    checkSufficientCredits,
    calculateImageGenerationCredits,
    refreshCredits,
    
    // 原始数据
    credits,
  };
}

// 预定义的操作配置
export const CreditOperationPresets = {
  // 静默操作（不显示提示）
  silent: {
    showSuccessToast: false,
    showErrorToast: false,
  },
  
  // 只显示错误提示
  errorOnly: {
    showSuccessToast: false,
    showErrorToast: true,
  },
  
  // 显示所有提示
  verbose: {
    showSuccessToast: true,
    showErrorToast: true,
  },
} as const;
