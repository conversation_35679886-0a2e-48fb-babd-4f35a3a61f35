/**
 * 积分系统缓存优化验证脚本
 * 用于验证缓存策略改进的效果
 */

const { performance } = require('perf_hooks');

// 模拟积分服务的核心功能
class MockCreditService {
  constructor() {
    this.cache = new Map();
    this.stats = { hits: 0, misses: 0, updates: 0 };
    this.dbQueryCount = 0;
    this.CACHE_TTL = 30 * 1000; // 30秒
  }

  // 模拟数据库查询
  async mockDbQuery(userUuid) {
    this.dbQueryCount++;
    // 模拟数据库查询延迟
    await new Promise(resolve => setTimeout(resolve, 50));
    return {
      left_credits: 100 + Math.floor(Math.random() * 100),
      is_pro: true,
      is_recharged: true
    };
  }

  // 获取用户积分（带缓存）
  async getUserCredits(userUuid) {
    const cached = this.cache.get(userUuid);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      this.stats.hits++;
      return cached.data;
    }

    this.stats.misses++;
    const data = await this.mockDbQuery(userUuid);
    this.cache.set(userUuid, {
      data,
      timestamp: Date.now(),
      hits: 0,
      version: 1
    });
    return data;
  }

  // 旧的缓存策略：清除缓存
  async updateCreditsOldWay(userUuid, creditChange) {
    // 模拟数据库更新
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 清除缓存
    this.cache.delete(userUuid);
  }

  // 新的缓存策略：直接更新缓存
  async updateCreditsNewWay(userUuid, creditChange) {
    // 模拟数据库更新
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // 直接更新缓存
    const existing = this.cache.get(userUuid);
    if (existing) {
      const newData = {
        ...existing.data,
        left_credits: existing.data.left_credits + creditChange
      };
      this.cache.set(userUuid, {
        data: newData,
        timestamp: Date.now(),
        hits: existing.hits,
        version: existing.version + 1
      });
      this.stats.updates++;
    }
  }

  getCacheStats() {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? (this.stats.hits / total * 100).toFixed(2) : '0.00';
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size,
      dbQueryCount: this.dbQueryCount
    };
  }

  reset() {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, updates: 0 };
    this.dbQueryCount = 0;
  }
}

// 测试场景
async function testCacheOptimization() {
  console.log('🚀 开始测试积分系统缓存优化效果\n');

  const service = new MockCreditService();
  const testUsers = ['user1', 'user2', 'user3', 'user4', 'user5'];

  // 测试1：缓存命中率对比
  console.log('📊 测试1: 缓存命中率对比');
  console.log('=' .repeat(50));

  // 旧策略测试
  service.reset();
  const oldStrategyStart = performance.now();
  
  for (let i = 0; i < 100; i++) {
    const userUuid = testUsers[i % testUsers.length];
    await service.getUserCredits(userUuid);
    
    // 模拟积分操作
    if (i % 10 === 0) {
      await service.updateCreditsOldWay(userUuid, 10);
    }
  }
  
  const oldStrategyTime = performance.now() - oldStrategyStart;
  const oldStats = service.getCacheStats();
  
  console.log('旧策略（清除缓存）结果:');
  console.log(`  缓存命中率: ${oldStats.hitRate}`);
  console.log(`  数据库查询次数: ${oldStats.dbQueryCount}`);
  console.log(`  执行时间: ${oldStrategyTime.toFixed(2)}ms`);
  console.log();

  // 新策略测试
  service.reset();
  const newStrategyStart = performance.now();
  
  for (let i = 0; i < 100; i++) {
    const userUuid = testUsers[i % testUsers.length];
    await service.getUserCredits(userUuid);
    
    // 模拟积分操作
    if (i % 10 === 0) {
      await service.updateCreditsNewWay(userUuid, 10);
    }
  }
  
  const newStrategyTime = performance.now() - newStrategyStart;
  const newStats = service.getCacheStats();
  
  console.log('新策略（直接更新缓存）结果:');
  console.log(`  缓存命中率: ${newStats.hitRate}`);
  console.log(`  数据库查询次数: ${newStats.dbQueryCount}`);
  console.log(`  缓存更新次数: ${newStats.updates}`);
  console.log(`  执行时间: ${newStrategyTime.toFixed(2)}ms`);
  console.log();

  // 性能提升计算
  const hitRateImprovement = parseFloat(newStats.hitRate) - parseFloat(oldStats.hitRate);
  const queryReduction = ((oldStats.dbQueryCount - newStats.dbQueryCount) / oldStats.dbQueryCount * 100).toFixed(2);
  const timeImprovement = ((oldStrategyTime - newStrategyTime) / oldStrategyTime * 100).toFixed(2);

  console.log('📈 性能提升对比:');
  console.log(`  缓存命中率提升: +${hitRateImprovement.toFixed(2)}%`);
  console.log(`  数据库查询减少: ${queryReduction}%`);
  console.log(`  执行时间减少: ${timeImprovement}%`);
  console.log();

  // 测试2：批量操作性能
  console.log('📊 测试2: 批量操作性能对比');
  console.log('=' .repeat(50));

  // 批量操作 - 旧策略
  service.reset();
  const batchOldStart = performance.now();
  
  for (const userUuid of testUsers) {
    await service.getUserCredits(userUuid); // 预加载
  }
  
  for (let i = 0; i < 20; i++) {
    const userUuid = testUsers[i % testUsers.length];
    await service.updateCreditsOldWay(userUuid, 5);
    await service.getUserCredits(userUuid); // 操作后查询
  }
  
  const batchOldTime = performance.now() - batchOldStart;
  const batchOldStats = service.getCacheStats();

  // 批量操作 - 新策略
  service.reset();
  const batchNewStart = performance.now();
  
  for (const userUuid of testUsers) {
    await service.getUserCredits(userUuid); // 预加载
  }
  
  for (let i = 0; i < 20; i++) {
    const userUuid = testUsers[i % testUsers.length];
    await service.updateCreditsNewWay(userUuid, 5);
    await service.getUserCredits(userUuid); // 操作后查询
  }
  
  const batchNewTime = performance.now() - batchNewStart;
  const batchNewStats = service.getCacheStats();

  console.log('批量操作 - 旧策略:');
  console.log(`  缓存命中率: ${batchOldStats.hitRate}`);
  console.log(`  数据库查询次数: ${batchOldStats.dbQueryCount}`);
  console.log(`  执行时间: ${batchOldTime.toFixed(2)}ms`);
  console.log();

  console.log('批量操作 - 新策略:');
  console.log(`  缓存命中率: ${batchNewStats.hitRate}`);
  console.log(`  数据库查询次数: ${batchNewStats.dbQueryCount}`);
  console.log(`  缓存更新次数: ${batchNewStats.updates}`);
  console.log(`  执行时间: ${batchNewTime.toFixed(2)}ms`);
  console.log();

  const batchImprovement = ((batchOldTime - batchNewTime) / batchOldTime * 100).toFixed(2);
  console.log(`📈 批量操作性能提升: ${batchImprovement}%`);
  console.log();

  // 验收标准检查
  console.log('✅ 验收标准检查:');
  console.log('=' .repeat(50));
  
  const targetHitRate = 80;
  const actualHitRate = parseFloat(newStats.hitRate);
  
  console.log(`目标缓存命中率: ${targetHitRate}%`);
  console.log(`实际缓存命中率: ${actualHitRate}%`);
  console.log(`✅ 缓存命中率达标: ${actualHitRate >= targetHitRate ? '是' : '否'}`);
  
  const queryReductionTarget = 60;
  const actualQueryReduction = parseFloat(queryReduction);
  
  console.log(`目标查询减少: ${queryReductionTarget}%`);
  console.log(`实际查询减少: ${actualQueryReduction}%`);
  console.log(`✅ 查询减少达标: ${actualQueryReduction >= queryReductionTarget ? '是' : '否'}`);
  
  console.log('\n🎉 缓存优化测试完成！');
}

// 运行测试
testCacheOptimization().catch(console.error);
