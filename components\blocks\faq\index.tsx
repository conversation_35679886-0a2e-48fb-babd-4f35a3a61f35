"use client";

import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Section as SectionType, FAQGroup } from "@/types/blocks/section";
import { cn } from "@/lib/utils";

export default function FAQ({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  // 判断是否使用分组模式
  const hasGroups = section.groups && section.groups.length > 0;
  const hasItems = section.items && section.items.length > 0;

  return (
    <section id={section.name} className="py-32">
      <div className="container px-4 lg:px-8">
        <div className="text-center">
          {section.label && (
            <Badge className="text-xs font-medium bg-white/20 text-white border-white/30">{section.label}</Badge>
          )}
          <h2 className="mt-4 text-4xl font-semibold text-white drop-shadow-lg">{section.title}</h2>
          <p className="mt-6 font-medium text-brand-dark">
            {section.description}
          </p>
        </div>

        <div className="mx-auto mt-14 max-w-4xl">
            {hasGroups ? (
              // 分组模式：渲染分组手风琴
              <Accordion type="single" collapsible className="space-y-6">
                {section.groups?.map((group, groupIndex) => (
                  <div key={group.id || groupIndex} className="bg-black/35 backdrop-blur-sm border border-white/20 rounded-lg overflow-hidden">
                    {/* 分组标题 */}
                    <div className="bg-white/5 px-6 py-4 border-b border-white/10">
                      <h3 className="text-xl font-semibold text-white">
                        {group.title}
                      </h3>
                      {group.description && (
                        <p className="mt-2 text-sm text-brand-light">{group.description}</p>
                      )}
                    </div>

                    {/* 分组内的FAQ项目 */}
                    <div className="p-6">
                      <Accordion type="single" collapsible className="space-y-4">
                        {group.items.map((item, itemIndex) => (
                          <AccordionItem
                            key={itemIndex}
                            value={`group-${groupIndex}-item-${itemIndex}`}
                            className="border border-white/10 rounded-lg overflow-hidden bg-white/5"
                          >
                            <AccordionTrigger className="px-6 py-4 hover:no-underline hover:bg-white/5 transition-colors text-white">
                              <h4 className="font-semibold text-white text-left">{item.title}</h4>
                            </AccordionTrigger>
                            <AccordionContent className="px-6 pb-6">
                              <p className="text-brand-light leading-relaxed">{item.description}</p>
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    </div>
                  </div>
                ))}
              </Accordion>
            ) : hasItems ? (
              // 传统模式：直接渲染FAQ项目手风琴（向后兼容）
              <div className="bg-black/35 backdrop-blur-sm border border-white/20 rounded-lg overflow-hidden p-6">
                <Accordion type="single" collapsible className="space-y-4">
                  {section.items?.map((item, index) => (
                    <AccordionItem
                      key={index}
                      value={`item-${index}`}
                      className="border border-white/10 rounded-lg overflow-hidden bg-white/5"
                    >
                    <AccordionTrigger className="px-6 py-4 hover:no-underline hover:bg-white/5 transition-colors text-white">
                      <h3 className="font-semibold text-white text-left">{item.title}</h3>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                      <p className="text-brand-light leading-relaxed">{item.description}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
                </Accordion>
              </div>
            ) : (
              // 无数据状态
              <div className="text-center py-12">
                <p className="text-brand-light">暂无FAQ内容</p>
              </div>
            )}
        </div>
      </div>
    </section>
  );
}
