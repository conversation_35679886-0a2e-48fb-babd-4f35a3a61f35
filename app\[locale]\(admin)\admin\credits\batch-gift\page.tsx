"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Upload, Download, Play, Clock, CheckCircle, XCircle, Users } from 'lucide-react';

interface BatchOperation {
  userEmail: string;
  credits: number;
  reason?: string;
  expiredAt?: string;
}

interface BatchResult {
  success: boolean;
  summary: {
    totalOperations: number;
    successfulOperations: number;
    failedOperations: number;
    totalCreditsGifted: number;
    executionTime: number;
    cacheWarmedUp: boolean;
    averageTimePerOperation: string;
  };
  details: {
    operations: Array<{
      userEmail: string;
      success: boolean;
      credits?: number;
      error?: string;
    }>;
  };
}

export default function BatchGiftCreditsPage() {
  const [operations, setOperations] = useState<BatchOperation[]>([]);
  const [csvInput, setCsvInput] = useState('');
  const [defaultCredits, setDefaultCredits] = useState(100);
  const [defaultReason, setDefaultReason] = useState('批量积分奖励');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<BatchResult | null>(null);

  // 解析CSV输入
  const parseCsvInput = () => {
    if (!csvInput.trim()) {
      toast.error('请输入用户邮箱列表');
      return;
    }

    const lines = csvInput.trim().split('\n');
    const newOperations: BatchOperation[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const parts = line.split(',').map(p => p.trim());
      const email = parts[0];
      const credits = parts[1] ? parseInt(parts[1]) : defaultCredits;
      const reason = parts[2] || defaultReason;

      if (!email || !email.includes('@')) {
        toast.error(`第 ${i + 1} 行：邮箱格式无效`);
        return;
      }

      if (isNaN(credits) || credits <= 0) {
        toast.error(`第 ${i + 1} 行：积分数量无效`);
        return;
      }

      newOperations.push({
        userEmail: email,
        credits,
        reason
      });
    }

    if (newOperations.length === 0) {
      toast.error('没有有效的操作记录');
      return;
    }

    if (newOperations.length > 100) {
      toast.error('一次最多只能处理100个用户');
      return;
    }

    setOperations(newOperations);
    toast.success(`成功解析 ${newOperations.length} 个操作`);
  };

  // 执行批量操作
  const executeBatchGift = async () => {
    if (operations.length === 0) {
      toast.error('没有要执行的操作');
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/credits/batch-gift', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operations,
          defaultReason
        }),
      });

      const data = await response.json();

      if (response.ok && data.code === 0) {
        setResult(data.data);
        toast.success(`批量操作完成：${data.data.summary.successfulOperations}/${data.data.summary.totalOperations} 成功`);
      } else {
        throw new Error(data.message || '批量操作失败');
      }
    } catch (error) {
      console.error('Batch gift error:', error);
      toast.error(error instanceof Error ? error.message : '批量操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出结果
  const exportResults = () => {
    if (!result) return;

    const csvContent = [
      'Email,Credits,Success,Error',
      ...result.details.operations.map(op => 
        `${op.userEmail},${op.credits || 0},${op.success ? 'Yes' : 'No'},"${op.error || ''}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `batch-gift-results-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-white mb-2">批量赠送积分</h1>
        <p className="text-gray-400">
          高效的批量积分赠送工具，支持缓存预热和并行处理
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                批量操作设置
              </CardTitle>
              <CardDescription>
                输入用户邮箱列表和默认设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="defaultCredits">默认积分数量</Label>
                <Input
                  id="defaultCredits"
                  type="number"
                  min="1"
                  max="10000"
                  value={defaultCredits}
                  onChange={(e) => setDefaultCredits(parseInt(e.target.value) || 100)}
                />
              </div>

              <div>
                <Label htmlFor="defaultReason">默认赠送原因</Label>
                <Input
                  id="defaultReason"
                  value={defaultReason}
                  onChange={(e) => setDefaultReason(e.target.value)}
                  placeholder="积分奖励原因"
                />
              </div>

              <div>
                <Label htmlFor="csvInput">用户列表 (CSV格式)</Label>
                <Textarea
                  id="csvInput"
                  value={csvInput}
                  onChange={(e) => setCsvInput(e.target.value)}
                  placeholder="<EMAIL>,100,奖励原因&#10;<EMAIL>,200&#10;<EMAIL>"
                  rows={8}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  格式：邮箱,积分数量,原因（后两项可选）
                </p>
              </div>

              <div className="flex gap-2">
                <Button onClick={parseCsvInput} variant="outline" className="flex-1">
                  解析列表
                </Button>
                <Button 
                  onClick={executeBatchGift} 
                  disabled={operations.length === 0 || loading}
                  className="flex-1"
                >
                  {loading ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      执行中...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      执行批量操作
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 操作预览 */}
          {operations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  操作预览 ({operations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {operations.slice(0, 10).map((op, index) => (
                    <div key={index} className="flex justify-between items-center p-2 bg-gray-800 rounded">
                      <span className="text-sm">{op.userEmail}</span>
                      <span className="text-sm font-medium">{op.credits} 积分</span>
                    </div>
                  ))}
                  {operations.length > 10 && (
                    <div className="text-center text-gray-500 text-sm">
                      还有 {operations.length - 10} 个操作...
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 结果区域 */}
        <div className="space-y-6">
          {result && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    执行结果
                  </CardTitle>
                  <div className="flex justify-between items-center">
                    <CardDescription>
                      批量操作执行完成
                    </CardDescription>
                    <Button onClick={exportResults} variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      导出结果
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-green-500/10 p-3 rounded border border-green-500/20">
                      <div className="text-sm text-green-400">成功操作</div>
                      <div className="text-2xl font-bold text-green-300">
                        {result.summary.successfulOperations}
                      </div>
                    </div>
                    <div className="bg-red-500/10 p-3 rounded border border-red-500/20">
                      <div className="text-sm text-red-400">失败操作</div>
                      <div className="text-2xl font-bold text-red-300">
                        {result.summary.failedOperations}
                      </div>
                    </div>
                    <div className="bg-blue-500/10 p-3 rounded border border-blue-500/20">
                      <div className="text-sm text-blue-400">总积分</div>
                      <div className="text-2xl font-bold text-blue-300">
                        {result.summary.totalCreditsGifted}
                      </div>
                    </div>
                    <div className="bg-purple-500/10 p-3 rounded border border-purple-500/20">
                      <div className="text-sm text-purple-400">执行时间</div>
                      <div className="text-2xl font-bold text-purple-300">
                        {result.summary.executionTime}ms
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-gray-400 space-y-1">
                    <div>平均每操作时间: {result.summary.averageTimePerOperation}ms</div>
                    <div>缓存预热: {result.summary.cacheWarmedUp ? '是' : '否'}</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>详细结果</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="max-h-80 overflow-y-auto space-y-2">
                    {result.details.operations.map((op, index) => (
                      <div key={index} className={`flex items-center justify-between p-2 rounded ${
                        op.success ? 'bg-green-500/10 border border-green-500/20' : 'bg-red-500/10 border border-red-500/20'
                      }`}>
                        <div className="flex items-center gap-2">
                          {op.success ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                          <span className="text-sm">{op.userEmail}</span>
                        </div>
                        <div className="text-sm">
                          {op.success ? (
                            <span className="text-green-400">{op.credits} 积分</span>
                          ) : (
                            <span className="text-red-400">{op.error}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle>使用说明</CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-gray-400 space-y-2">
              <div>• 支持CSV格式输入：邮箱,积分,原因</div>
              <div>• 一次最多处理100个用户</div>
              <div>• 自动缓存预热提升性能</div>
              <div>• 并行处理减少执行时间</div>
              <div>• 详细的执行结果和错误信息</div>
              <div>• 支持导出结果为CSV文件</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
